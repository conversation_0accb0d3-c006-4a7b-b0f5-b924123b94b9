import React from 'react'
import type { Tab } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore'

/**
 * Asset Type Definition Interface
 * Defines the contract for asset type plugins
 */
export interface AssetTypeDefinition {
  /** Unique identifier for the asset type */
  id: string
  /** Human-readable display name */
  displayName: string
  /** Icon component for UI display */
  icon: React.ComponentType<{ className?: string }>
  /** Tab content component for this asset type */
  tabComponent: React.ComponentType<{ tab: Tab; dragTreeId: string }>
  /** Get color class for this asset type */
  getAssetTypeColor: () => string
  /** Create tab title from asset data */
  createTabTitle: (asset: any) => string
  /** Check if a type string matches this asset type */
  isAssetType: (type: string) => boolean
  /** Optional: Asset-specific tab creation logic */
  createAssetTabData?: (asset: any) => Partial<Tab['aiPaneData']>
}

/**
 * Global Asset Type Registry
 * Central registry for all asset type definitions
 */
export class AssetTypeRegistry {
  private registry = new Map<string, AssetTypeDefinition>()

  /**
   * Register a new asset type
   */
  register(definition: AssetTypeDefinition): void {
    if (this.registry.has(definition.id)) {
      console.warn(
        `Asset type '${definition.id}' is already registered. Overwriting.`
      )
    }
    this.registry.set(definition.id, definition)
    console.log(`✅ Registered asset type: ${definition.id}`)
  }

  /**
   * Get asset type definition by ID
   */
  get(id: string): AssetTypeDefinition | undefined {
    return this.registry.get(id)
  }

  /**
   * Check if an asset type is registered
   */
  has(id: string): boolean {
    return this.registry.has(id)
  }

  /**
   * Get all registered asset type IDs
   */
  getRegisteredTypes(): string[] {
    return Array.from(this.registry.keys())
  }

  /**
   * Get all registered asset type definitions
   */
  getAllDefinitions(): AssetTypeDefinition[] {
    return Array.from(this.registry.values())
  }

  /**
   * Validate if a type is a valid asset type
   */
  isValidAssetType(type: string): boolean {
    return this.registry.has(type)
  }

  /**
   * Get asset type definition with fallback
   */
  getWithFallback(
    id: string,
    fallback?: AssetTypeDefinition
  ): AssetTypeDefinition | undefined {
    return this.registry.get(id) || fallback
  }

  /**
   * Unregister an asset type (useful for testing)
   */
  unregister(id: string): boolean {
    return this.registry.delete(id)
  }

  /**
   * Clear all registrations (useful for testing)
   */
  clear(): void {
    this.registry.clear()
  }
}

/**
 * Global asset type registry instance
 */
export const assetTypeRegistry = new AssetTypeRegistry()

/**
 * Helper function to register an asset type
 */
export const registerAssetType = (definition: AssetTypeDefinition): void => {
  assetTypeRegistry.register(definition)
}

/**
 * Helper function to get an asset type definition
 */
export const getAssetType = (id: string): AssetTypeDefinition | undefined => {
  return assetTypeRegistry.get(id)
}

/**
 * Helper function to check if an asset type is registered
 */
export const isValidAssetType = (type: string): boolean => {
  return assetTypeRegistry.isValidAssetType(type)
}

/**
 * Type guard for asset types
 */
export const isAssetTab = (tab: Tab): boolean => {
  return tab.aiPaneData !== undefined && isValidAssetType(tab.type)
}
