/**
 * Debug utility for controlled logging
 * Replaces console.* calls with environment-controlled logging
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error'

/**
 * Check if debug mode is enabled
 */
function isDebugEnabled(): boolean {
  // Check both client and server environment variables
  return (
    process.env.NODE_ENV === 'development' ||
    process.env.NEXT_PUBLIC_DEBUG === 'true' ||
    process.env.DEBUG === 'true'
  )
}

/**
 * Create namespaced logger
 */
export function createLogger(namespace: string) {
  const prefix = `[${namespace}]`

  return {
    debug: (...args: any[]) => {
      if (isDebugEnabled()) {
        console.debug(prefix, ...args)
      }
    },
    info: (...args: any[]) => {
      if (isDebugEnabled()) {
        console.info(prefix, ...args)
      }
    },
    warn: (...args: any[]) => {
      console.warn(prefix, ...args)
    },
    error: (...args: any[]) => {
      console.error(prefix, ...args)
    },
    log: (...args: any[]) => {
      if (isDebugEnabled()) {
        console.log(prefix, ...args)
      }
    },
  }
}

/**
 * Default debug logger
 */
export const debug = createLogger('APP')

/**
 * Performance timing utility
 */
export function createTimer(label: string) {
  const start = Date.now()
  const logger = createLogger('PERF')

  return {
    end: () => {
      const duration = Date.now() - start
      logger.debug(`${label} took ${duration}ms`)
      return duration
    },
  }
}

/**
 * Safe error logging that won't throw
 */
export function safeError(
  message: string,
  error?: any,
  context?: Record<string, any>
) {
  try {
    const logger = createLogger('ERROR')
    if (context) {
      logger.error(message, { error, context })
    } else {
      logger.error(message, error)
    }
  } catch (e) {
    // Fallback to console if logger fails
    console.error('Logger failed:', e)
    console.error('Original error:', message, error)
  }
}

/**
 * Conditional debug for specific features
 */
export function createFeatureLogger(feature: string) {
  const envVar = `NEXT_PUBLIC_DEBUG_${feature.toUpperCase()}`
  const isEnabled = process.env[envVar] === 'true' || isDebugEnabled()

  return createLogger(feature.toUpperCase())
}
