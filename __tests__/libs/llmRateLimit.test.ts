import {
  enforceRateLimit,
  _resetRateLimitCounters,
} from '@/app/libs/llmRateLimit'
jest.mock('next/server', () => ({
  NextResponse: {
    json: (_body: any, init?: any) => ({ status: init?.status }),
  },
}))
import { NextResponse } from 'next/server'

describe('enforceRateLimit', () => {
  const mockSession = (id: string, tier: any = 'FREE') =>
    ({
      user: { id, subscription_tier: tier },
    }) as any

  beforeEach(() => {
    _resetRateLimitCounters()
  })

  it('allows first request within window', async () => {
    const res = await enforceRateLimit(mockSession('u1'), 'screening_rephrase')
    expect(res).toBeNull()
  })

  it('blocks when maxRequests exceeded', async () => {
    // Config for FREE tier, screening_rephrase has maxRequests default 1
    await enforceRateLimit(mockSession('u2'), 'screening_rephrase') // first ok
    const res2 = await enforceRateLimit(mockSession('u2'), 'screening_rephrase') // second blocked
    expect(res2?.status).toBe(429)
  })

  it('resets after window', async () => {
    jest.useFakeTimers()
    const session = mockSession('u3')
    // first call ok
    expect(await enforceRateLimit(session, 'screening_rephrase')).toBeNull()
    // advance time 2s (window 1s)
    jest.advanceTimersByTime(6000)
    // flush timers
    await Promise.resolve()
    const res = await enforceRateLimit(session, 'screening_rephrase')
    expect(res).toBeNull()
    jest.useRealTimers()
  })
})
