/**
 * LLM Model Configuration Validation Tests
 *
 * Simple tests to ensure configuration completeness and token limits are correct.
 */

import { SubscriptionTier } from '@prisma/client'
import {
  LLM_MODEL_CONFIG,
  validateModelConfig,
  type APIRoute,
} from '@/app/config/llm-models'

describe('LLM Model Configuration Validation', () => {
  test('configuration must be valid for build to succeed', () => {
    const isValid = validateModelConfig()

    if (!isValid) {
      throw new Error(
        'LLM Model Configuration is incomplete! ' +
          'Check console output for missing configurations.'
      )
    }

    expect(isValid).toBe(true)
  })

  test('FREE tier should have at least 4K+ tokens to avoid typos', () => {
    const freeConfig = LLM_MODEL_CONFIG.FREE
    const routes: APIRoute[] = [
      'dragtree_generate_questions',
      'dragtree_research_generate',
      'dragtree_generate_similar_questions',
      'dragtree_generate_similar_categories',
      'screening_diagnose',
      'screening_rephrase',
      'aipane_generate',
      'aipane_chat',
    ]

    for (const route of routes) {
      const config = freeConfig[route]
      // FREE tier should have at least 4000 tokens to avoid typos
      expect(config.maxOutputTokens).toBeGreaterThanOrEqual(4000)
    }
  })

  test('FREE tier should have smaller max tokens than other tiers', () => {
    const freeConfig = LLM_MODEL_CONFIG.FREE
    const guestConfig = LLM_MODEL_CONFIG.GUEST
    const routes: APIRoute[] = [
      'dragtree_generate_questions',
      'dragtree_research_generate',
      'dragtree_generate_similar_questions',
      'dragtree_generate_similar_categories',
      'screening_diagnose',
      'screening_rephrase',
      'aipane_generate',
      'aipane_chat',
    ]

    for (const route of routes) {
      const freeTokens = freeConfig[route].maxOutputTokens
      const guestTokens = guestConfig[route].maxOutputTokens

      // FREE tier should have smaller token limits than other tiers
      expect(freeTokens).toBeLessThan(guestTokens!)
    }
  })
})
