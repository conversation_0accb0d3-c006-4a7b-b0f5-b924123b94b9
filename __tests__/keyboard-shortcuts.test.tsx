import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { useShiftEnterSubmit } from '@/app/hooks/useShiftEnterSubmit'

// Mock toast
jest.mock('react-hot-toast', () => ({
  toast: {
    error: jest.fn(),
  },
}))

// Test component that uses the hook
const TestComponent: React.FC<{
  input: string
  minCharCount: number
  onSubmit: () => void
}> = ({ input, minCharCount, onSubmit }) => {
  const { formRef, onKeyDown } = useShiftEnterSubmit(input, minCharCount)

  return (
    <form ref={formRef} onSubmit={onSubmit}>
      <textarea
        data-testid="test-textarea"
        value={input}
        onKeyDown={onKeyDown}
        onChange={() => {}}
      />
    </form>
  )
}

describe('Keyboard Shortcuts', () => {
  describe('useShiftEnterSubmit hook', () => {
    it('should submit on Enter key (not Shift+Enter)', () => {
      const mockSubmit = jest.fn()
      const input = 'Test message with enough characters'

      render(
        <TestComponent input={input} minCharCount={5} onSubmit={mockSubmit} />
      )

      const textarea = screen.getByTestId('test-textarea')

      // Test Enter key (should submit)
      fireEvent.keyDown(textarea, {
        key: 'Enter',
        shiftKey: false,
      })

      expect(mockSubmit).toHaveBeenCalled()
    })

    it('should NOT submit on Shift+Enter (should allow new line)', () => {
      const mockSubmit = jest.fn()
      const input = 'Test message with enough characters'

      render(
        <TestComponent input={input} minCharCount={5} onSubmit={mockSubmit} />
      )

      const textarea = screen.getByTestId('test-textarea')

      // Test Shift+Enter (should NOT submit)
      fireEvent.keyDown(textarea, {
        key: 'Enter',
        shiftKey: true,
      })

      expect(mockSubmit).not.toHaveBeenCalled()
    })

    it('should not submit if input is below minimum character count', () => {
      const mockSubmit = jest.fn()
      const input = 'Hi' // Too short

      render(
        <TestComponent input={input} minCharCount={10} onSubmit={mockSubmit} />
      )

      const textarea = screen.getByTestId('test-textarea')

      // Test Enter key with short input
      fireEvent.keyDown(textarea, {
        key: 'Enter',
        shiftKey: false,
      })

      expect(mockSubmit).not.toHaveBeenCalled()
    })
  })
})
