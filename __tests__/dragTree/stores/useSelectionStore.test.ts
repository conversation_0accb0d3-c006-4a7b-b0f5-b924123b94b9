/**
 * Unit tests for useSelectionStore - Selection state management
 *
 * Tests core selection functionality including:
 * - Selection mode state transitions
 * - Node selection/deselection
 * - Rectangle drawing state
 * - Context modal triggers
 * - State cleanup operations
 */

import { act, renderHook } from '@testing-library/react'
import {
  useSelectionStore,
  SelectionRectangle,
} from '@/app/(conv)/dragTree/[dragTreeId]/stores/useSelectionStore'

describe('useSelectionStore', () => {
  beforeEach(() => {
    // Reset store state before each test
    act(() => {
      useSelectionStore.getState().clearAll()
      useSelectionStore.setState({
        isSelectionMode: false,
        showContextSelectionModal: false,
      })
    })
  })

  describe('Selection Mode State Transitions', () => {
    it('should toggle selection mode correctly', () => {
      const { result } = renderHook(() => useSelectionStore())

      expect(result.current.isSelectionMode).toBe(false)

      act(() => {
        result.current.toggleSelectionMode()
      })

      expect(result.current.isSelectionMode).toBe(true)

      act(() => {
        result.current.toggleSelectionMode()
      })

      expect(result.current.isSelectionMode).toBe(false)
    })

    it('should set selection mode directly', () => {
      const { result } = renderHook(() => useSelectionStore())

      act(() => {
        result.current.setSelectionMode(true)
      })

      expect(result.current.isSelectionMode).toBe(true)

      act(() => {
        result.current.setSelectionMode(false)
      })

      expect(result.current.isSelectionMode).toBe(false)
    })

    it('should clear all selections when exiting selection mode', () => {
      const { result } = renderHook(() => useSelectionStore())

      // Add some selections
      act(() => {
        result.current.setSelectedNodes(['node1', 'node2'], ['node3'])
        result.current.setSelectionMode(true)
      })

      expect(result.current.selectedNodeIds.size).toBe(2)
      expect(result.current.nonResearchedNodeIds.size).toBe(1)

      // Exit selection mode
      act(() => {
        result.current.setSelectionMode(false)
      })

      expect(result.current.selectedNodeIds.size).toBe(0)
      expect(result.current.nonResearchedNodeIds.size).toBe(0)
    })
  })

  describe('Node Selection Management', () => {
    it('should add and remove selected nodes', () => {
      const { result } = renderHook(() => useSelectionStore())

      act(() => {
        result.current.addSelectedNode('node1')
      })

      expect(result.current.selectedNodeIds.has('node1')).toBe(true)
      expect(result.current.getSelectedCount()).toBe(1)

      act(() => {
        result.current.removeSelectedNode('node1')
      })

      expect(result.current.selectedNodeIds.has('node1')).toBe(false)
      expect(result.current.getSelectedCount()).toBe(0)
    })

    it('should toggle node selection', () => {
      const { result } = renderHook(() => useSelectionStore())

      // Toggle on
      act(() => {
        result.current.toggleSelectedNode('node1')
      })

      expect(result.current.selectedNodeIds.has('node1')).toBe(true)

      // Toggle off
      act(() => {
        result.current.toggleSelectedNode('node1')
      })

      expect(result.current.selectedNodeIds.has('node1')).toBe(false)
    })

    it('should set multiple selected nodes at once', () => {
      const { result } = renderHook(() => useSelectionStore())

      act(() => {
        result.current.setSelectedNodes(
          ['node1', 'node2', 'node3'],
          ['node4', 'node5']
        )
      })

      expect(result.current.selectedNodeIds.size).toBe(3)
      expect(result.current.nonResearchedNodeIds.size).toBe(2)
      expect(result.current.getSelectedCount()).toBe(3) // Only counts researched nodes

      expect(result.current.selectedNodeIds.has('node1')).toBe(true)
      expect(result.current.selectedNodeIds.has('node2')).toBe(true)
      expect(result.current.selectedNodeIds.has('node3')).toBe(true)
      expect(result.current.nonResearchedNodeIds.has('node4')).toBe(true)
      expect(result.current.nonResearchedNodeIds.has('node5')).toBe(true)
    })

    it('should clear all selected nodes', () => {
      const { result } = renderHook(() => useSelectionStore())

      act(() => {
        result.current.setSelectedNodes(['node1', 'node2'], ['node3'])
      })

      expect(result.current.getSelectedCount()).toBe(2)

      act(() => {
        result.current.clearSelectedNodes()
      })

      expect(result.current.selectedNodeIds.size).toBe(0)
      expect(result.current.nonResearchedNodeIds.size).toBe(0)
      expect(result.current.getSelectedCount()).toBe(0)
    })

    it('should remove non-researched nodes', () => {
      const { result } = renderHook(() => useSelectionStore())

      act(() => {
        result.current.setSelectedNodes(['node1'], ['node2', 'node3'])
      })

      expect(result.current.nonResearchedNodeIds.has('node2')).toBe(true)
      expect(result.current.nonResearchedNodeIds.has('node3')).toBe(true)

      act(() => {
        result.current.removeNonResearchedNode('node2')
      })

      expect(result.current.nonResearchedNodeIds.has('node2')).toBe(false)
      expect(result.current.nonResearchedNodeIds.has('node3')).toBe(true)
    })
  })

  describe('Rectangle Drawing State', () => {
    const mockRectangle: SelectionRectangle = {
      id: 'rect1',
      startX: 10,
      startY: 20,
      endX: 100,
      endY: 200,
      isActive: true,
    }

    it('should start drawing rectangle', () => {
      const { result } = renderHook(() => useSelectionStore())

      act(() => {
        result.current.startDrawing(10, 20)
      })

      expect(result.current.isDrawing).toBe(true)
      expect(result.current.currentRectangle).toBeTruthy()
      expect(result.current.currentRectangle?.startX).toBe(10)
      expect(result.current.currentRectangle?.startY).toBe(20)
    })

    it('should update drawing rectangle', () => {
      const { result } = renderHook(() => useSelectionStore())

      act(() => {
        result.current.startDrawing(10, 20)
        result.current.updateDrawing(100, 200)
      })

      expect(result.current.currentRectangle?.endX).toBe(100)
      expect(result.current.currentRectangle?.endY).toBe(200)
    })

    it('should finish drawing and add rectangle to list', () => {
      const { result } = renderHook(() => useSelectionStore())

      act(() => {
        result.current.startDrawing(10, 20)
        result.current.updateDrawing(100, 200)
        result.current.finishDrawing()
      })

      expect(result.current.isDrawing).toBe(false)
      expect(result.current.currentRectangle).toBe(null)
      expect(result.current.selectionRectangles.length).toBe(1)

      const rectangle = result.current.selectionRectangles[0]
      expect(rectangle.startX).toBe(10)
      expect(rectangle.startY).toBe(20)
      expect(rectangle.endX).toBe(100)
      expect(rectangle.endY).toBe(200)
      expect(rectangle.isActive).toBe(false)
    })

    it('should cancel drawing', () => {
      const { result } = renderHook(() => useSelectionStore())

      act(() => {
        result.current.startDrawing(10, 20)
        result.current.cancelDrawing()
      })

      expect(result.current.isDrawing).toBe(false)
      expect(result.current.currentRectangle).toBe(null)
      expect(result.current.selectionRectangles.length).toBe(0)
    })

    it('should clear all rectangles', () => {
      const { result } = renderHook(() => useSelectionStore())

      act(() => {
        result.current.startDrawing(10, 20)
        result.current.finishDrawing()
        result.current.startDrawing(30, 40)
        result.current.finishDrawing()
      })

      expect(result.current.selectionRectangles.length).toBe(2)

      act(() => {
        result.current.clearRectangles()
      })

      expect(result.current.selectionRectangles.length).toBe(0)
    })
  })

  describe('Context Modal State', () => {
    it('should toggle context selection modal', () => {
      const { result } = renderHook(() => useSelectionStore())

      expect(result.current.showContextSelectionModal).toBe(false)

      act(() => {
        result.current.setShowContextSelectionModal(true)
      })

      expect(result.current.showContextSelectionModal).toBe(true)

      act(() => {
        result.current.setShowContextSelectionModal(false)
      })

      expect(result.current.showContextSelectionModal).toBe(false)
    })
  })

  describe('Complete State Cleanup', () => {
    it('should clear all state with clearAll', () => {
      const { result } = renderHook(() => useSelectionStore())

      // Set up complex state
      act(() => {
        result.current.setSelectedNodes(['node1', 'node2'], ['node3'])
        result.current.startDrawing(10, 20)
        result.current.finishDrawing()
        result.current.setSelectionMode(true)
      })

      expect(result.current.selectedNodeIds.size).toBe(2)
      expect(result.current.nonResearchedNodeIds.size).toBe(1)
      expect(result.current.selectionRectangles.length).toBe(1)
      expect(result.current.isSelectionMode).toBe(true)

      // Clear everything
      act(() => {
        result.current.clearAll()
      })

      expect(result.current.selectedNodeIds.size).toBe(0)
      expect(result.current.nonResearchedNodeIds.size).toBe(0)
      expect(result.current.selectionRectangles.length).toBe(0)
      expect(result.current.isDrawing).toBe(false)
      expect(result.current.currentRectangle).toBe(null)
    })
  })
})
