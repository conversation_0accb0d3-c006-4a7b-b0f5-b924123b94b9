/**
 * Language Preference Tests
 *
 * Tests for the language preference functionality in drag trees,
 * including server actions and language prompt integration.
 */

// Mock dependencies first
jest.mock('@/app/libs/prismadb', () => ({
  dragTree: {
    update: jest.fn(),
  },
}))

jest.mock('next/cache', () => ({
  revalidatePath: jest.fn(),
}))

jest.mock('next-auth', () => ({
  getServerSession: jest.fn(),
}))

jest.mock('@/app/api/auth/authOptions', () => ({
  authOptions: {},
}))

import {
  getLanguageName,
  getLLMLanguageName,
  type SupportedLanguageCode,
  SUPPORTED_LANGUAGES,
} from '@/app/(conv)/screening/constants/languages'

describe('Language Preference Functionality', () => {
  describe('Language Constants', () => {
    test('should have all required language constants', () => {
      expect(SUPPORTED_LANGUAGES.EN).toBe('en')
      expect(SUPPORTED_LANGUAGES.CN).toBe('cn')
      expect(SUPPORTED_LANGUAGES.ES).toBe('es')
      expect(SUPPORTED_LANGUAGES.JP).toBe('jp')
    })

    test('should return correct language names', () => {
      expect(getLanguageName('en')).toBe('English')
      expect(getLanguageName('cn')).toBe('中文 (Chinese)')
      expect(getLanguageName('es')).toBe('Español (Spanish)')
      expect(getLanguageName('jp')).toBe('日本語 (Japanese)')
    })

    test('should return correct LLM language names', () => {
      expect(getLLMLanguageName('en')).toBe('English')
      expect(getLLMLanguageName('cn')).toBe('Chinese')
      expect(getLLMLanguageName('es')).toBe('Spanish')
      expect(getLLMLanguageName('jp')).toBe('Japanese')
    })

    test('should handle invalid language codes gracefully', () => {
      // @ts-expect-error Testing invalid input
      expect(getLanguageName('invalid')).toBe('English') // Should fallback to default
      // @ts-expect-error Testing invalid input
      expect(getLLMLanguageName('invalid')).toBe('English') // Should fallback to default
    })
  })

  describe('Language Preference Database Schema', () => {
    test('should support all required language codes', () => {
      const supportedLanguages: SupportedLanguageCode[] = [
        'en',
        'cn',
        'es',
        'jp',
      ]

      // Verify that all supported languages have proper string values
      supportedLanguages.forEach(lang => {
        expect(typeof lang).toBe('string')
        expect(lang.length).toBeGreaterThan(0)
        expect(lang.length).toBeLessThanOrEqual(5) // Database field constraint
      })
    })

    test('should have default language as English', () => {
      expect(SUPPORTED_LANGUAGES.EN).toBe('en')
    })
  })

  describe('Language Integration', () => {
    test('should have consistent language support across all dragtree LLM routes', () => {
      // This test ensures that all the language constants and utilities
      // are properly exported and can be used by the LLM API routes
      const supportedLanguages = Object.values(SUPPORTED_LANGUAGES)

      supportedLanguages.forEach(lang => {
        expect(getLanguageName(lang as SupportedLanguageCode)).toBeTruthy()
        expect(getLLMLanguageName(lang as SupportedLanguageCode)).toBeTruthy()
      })
    })

    test('should provide clean language names for LLM instructions', () => {
      // LLM language names should not contain mixed characters for better prompt clarity
      expect(getLLMLanguageName('cn')).toBe('Chinese') // Not '中文 (Chinese)'
      expect(getLLMLanguageName('es')).toBe('Spanish') // Not 'Español (Spanish)'
      expect(getLLMLanguageName('jp')).toBe('Japanese') // Not '日本語 (Japanese)'
    })
  })
})
