{"permissions": {"allow": ["<PERSON><PERSON>(tail:*)", "Bash(rg:*)", "Bash(npm run build:*)", "Bash(npm test:*)", "<PERSON><PERSON>(mv:*)", "Bash(find:*)", "Bash(npm run lint)", "Bash(npm run typecheck:*)", "Bash(npm run:*)", "WebFetch(domain:reactflow.dev)", "WebFetch(domain:github.com)", "Bash(npm install)", "Bash(grep:*)", "<PERSON><PERSON>(curl:*)", "mcp__ide__getDiagnostics", "WebFetch(domain:ai-sdk.dev)", "WebFetch(domain:vercel.com)", "WebFetch(domain:ai-sdk-reasoning.vercel.app)", "WebFetch(domain:sdk.vercel.ai)", "WebFetch(domain:www.assistant-ui.com)", "WebFetch(domain:www.assistant-ui.com)", "Bash(gemini:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npx tsc:*)", "Bash(npx eslint:*)", "Bash(ls:*)", "Bash(npx prisma migrate dev:*)", "<PERSON><PERSON>(timeout:*)", "WebFetch(domain:docs.anthropic.com)"], "deny": ["<PERSON><PERSON>(sudo:*)", "Bash(doas:*)", "Bash(systemctl:*)", "<PERSON><PERSON>(launchctl:*)", "Bash(rm -rf:*)", "Bash(rm --recursive:*)", "Bash(rm -r:*/)", "<PERSON><PERSON>(chmod 7[0-7][0-7]:*)", "Bash(chown:*)", "Bash(dd if=* of=/dev/*)", "Bash(mkfs:*)", "Bash(diskutil eraseDisk:*)", "<PERSON><PERSON>(diskutil unmountDisk:*)", "<PERSON><PERSON>(shred:*)", "Bash(mount:*)", "Bash(umount:*)", "Bash(prisma db push:*)", "Bash(prisma migrate reset:*)", "Bash(prisma db deploy:*)", "Bash(brew install:*)", "Bash(brew upgrade:*)", "Bash(apt-get:*)", "Bash(yum:*)", "Bash(pip install --system:*)", "Bash(npm install -g:*)", "Bash(yarn global add:*)", "<PERSON><PERSON>(reboot)", "Bash(shutdown:*)", "<PERSON><PERSON>(poweroff)", "Bash(init 0)", "<PERSON><PERSON>(init 6)", "<PERSON><PERSON>(kill -9:*)", "<PERSON><PERSON>(killall -9:*)", "<PERSON><PERSON>(pkill -9:*)", "Bash(curl *|*sh*)", "Bash(wget *|*sh*)", "<PERSON><PERSON>(scp:*)", "Bash(rsync:*)", "Bash(ssh:*)", "Bash(nc -e:*)", "Bash(netcat -e:*)"]}, "hooks": {"Notification": [{"hooks": [{"type": "command", "command": "bash ./scripts/notify_telegram.sh \"$CLAUDE_NOTIFICATION\" \"Notification\" \"$CLAUDE_HOOK_METADATA\"", "run_in_background": true}]}], "Stop": [{"hooks": [{"type": "command", "command": "bash ./scripts/notify_telegram.sh \"✅ <PERSON> finished.\" \"Stop\" \"{\\\"event\\\": \\\"Stop\\\", \\\"timestamp\\\": \\\"$(date -Iseconds)\\\", \\\"status\\\": \\\"completed\\\"}\"", "run_in_background": true}]}]}}