# AI-SDK 5 Migration – DragTree & Screening

**Status:** Draft 2025-08-01

---

Ref: https://vercel.com/blog/ai-sdk-5

## 1 Objective

Migrate our codebase from AI-SDK 4.x to **AI-SDK 5** while keeping all existing DragTree & Screening functionality intact.
Goals:

1. Remove deprecated v4 APIs before GA launch.
2. Enable data-parts streaming (title, citations, progress) for future agentic features.
3. Keep Prisma storage _normalized_ – store each message and each data-part in its own row instead of persisting the whole `messages[]` array blob.

## 2 Scope

• **In scope** — files under:

```
app/(conv)/dragTree/**
app/(conv)/screening/**
app/api/{dragtree,screening,aipane}/**
__tests__/dragTree/**
```

• **Out of scope** — landing pages, subscription flows, notebook, etc.

## 3 Current State Analysis

```
client
│ useChat
│   ├─ hooks/useAiConversation.ts          (title & step parsing)
│   ├─ hooks/useAiGeneration.ts            (title streaming)
│   ├─ hooks/useResearchLifecycle.ts       (web-search citations)
│   └─ screening/hooks/useScreeningRephrase.ts
│ useCompletion
│   └─ dragTree/components/Client.tsx
└ tests (jest mocks)

server
│ streamText()
│   ├─ api/dragtree/generate_questions/route.ts
│   ├─ api/screening/rephrase/*.ts
│   └─ api/aipane/chat/route.ts
```

No `createUIMessageStream` yet; titles and execution-steps are currently injected via the custom `data` array on the client.

## 4 Design Changes

### 4.1 Dependencies

```bash
npm i ai@^5 @ai-sdk/azure@latest       # azure pkg already matches major
npx @ai-sdk/codemod upgrade            # converts import paths & basic API
```

### 4.2 Type strategy

We _do not_ introduce a bespoke message interface to avoid type bloat.

```ts
import { UIMessage } from 'ai'
export type Msg = UIMessage<any, any, any>
```

Every `useChat` instance becomes `useChat<Msg>()`. Further specialisation (e.g. citation parts) can be added later.

### 4.3 Client hook updates

1. **Replace** `data` array access with data-parts inspection:

```diff
- const latest = chat.data?.at(-1)
+ const lastMsg = messages[messages.length-1]
+ lastMsg?.dataParts.forEach(part => {
+   if (part.type === 'data-title') ...
+ })
```

2. Keep existing throttle, custom `fetch`, etc.—APIs unchanged.
3. Jest mocks: import path changes, generics stubbed with `any`.

### 4.4 Server streaming pattern

For endpoints that need extra payload (title, citations, steps):

```ts
import { createUIMessageStream, streamText } from 'ai'

export async function POST(req: NextRequest) {
  const stream = createUIMessageStream<Msg>({
    async execute({ writer }) {
      // 1️⃣ Start LLM stream
      await writer.pipeText(streamText({ model, messages }))

      // 2️⃣ Emit generated title
      writer.write({ type: 'data-title', data: { title } })

      // 3️⃣ Optionally emit citations
      writer.write({ type: 'data-citations', data: { list: citations } })
    },
  })
  return stream.toAIStreamResponse()
}
```

Endpoints that stream **only** plain assistant text can keep `streamText()` + `toDataStreamResponse()`.

### 4.5 Prisma schema alignment

We already store supplemental reasoning/tool data in **`ai_execution_steps`** (see `AiExecutionStep` model). Instead of introducing a new `ai_data_part` table we will **reuse that table** by extending the `AiStepType` enum.

Proposed additional enum members:

```
TITLE              // final title for the conversation / generation
CITATIONS          // list of { url, snippet, metadata }
STATUS_UPDATE      // arbitrary progress or state text (optional)
```

Write-path rules

1. Every streamed assistant message is still persisted to `ai_messages` (role = ASSISTANT).
2. For each data-part we call `createExecutionStep` with:
   - `messageId` – id of the assistant message being streamed
   - `stepOrder` – incremental counter (0-n)
   - `type` – one of the new enum members (e.g. `TITLE`)
   - `metadata` – JSON payload containing `{ title: string }`, `{ items: Citation[] }`, etc.

This keeps our schema normalised and avoids another migration while giving us the flexibility to attach any future parts (e.g. structured tool results) with zero additional tables.

## 5 Migration Plan

| #   | Step                                                             | Owner  | Notes                             |
| --- | ---------------------------------------------------------------- | ------ | --------------------------------- |
| 1   | Branch `feat/ai-sdk5`                                            | FE     |                                   |
| 2   | Bump deps + run codemod                                          | FE     | commit                            |
| 3   | Fix TS errors in 4 hooks                                         | FE     | data-parts refactor               |
| 4   | Adjust jest mocks                                                | QA     | green unit tests                  |
| 5   | Smoke test DragTree & Screening UIs                              | QA     | streaming still works             |
| 6   | Convert `api/aipane/chat` to UIMessageStream                     | BE     | emits `data-title` today          |
| 7   | Extend `AiStepType` enum (TITLE, CITATIONS) & generate migration | BE     | additive, backward-compatible     |
| 8   | Persist parts as `AiExecutionStep` rows in chat route            | BE     | refactor after SDK upgrade        |
| 9   | E2E Playwright run                                               | QA     | dragtree flow, screening rephrase |
| 10  | Merge & deploy to staging                                        | DevOps |

Estimated effort: **1 day FE**, **½ day BE**, **½ day QA**.

## 6 Risk & Mitigation

| Risk                                    | Impact          | Mitigation                                 |
| --------------------------------------- | --------------- | ------------------------------------------ |
| Subtle API change in streaming throttle | UI jank         | manual perf testing on staging             |
| Data-part persistence race              | lost metadata   | persist _after_ writer closed, wrap in txn |
| Rollback needs DB schema                | migration guard | separate new tables, no column drops       |

## 7 Rollback Strategy

Revert branch, reinstall `ai@4.x`, redeploy. New `ai_data_part` table is additive and harmless if unused.

## 8 Open Questions

1. Should **screening** store each numbered suggestion as separate data-part?
2. Do we back-fill existing chat records with titles or compute lazily on first read?
