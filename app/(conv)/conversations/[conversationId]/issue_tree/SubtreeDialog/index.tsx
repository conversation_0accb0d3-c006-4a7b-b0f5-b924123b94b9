import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog'

import toast from 'react-hot-toast'
import {
  findNeighbors,
  nodesToDict,
  constructMarkdown,
  mergeSubtreeIntoIssueTree,
  constructDisplayMarkdown,
} from './utils'
import { Node, Edge } from '@/app/types'
import React from 'react'
import { useEffect, useCallback, useState } from 'react'
import markdownToReactFlow from '@/app/(conv)/conversations/[conversationId]/issue_tree/markdownToReactFlow'
import ReactFlow, {
  Controls,
  Background,
  BackgroundVariant,
  ReactFlowProvider,
  getIncomers,
  getOutgoers,
} from 'reactflow'
import HoverButton from './HoverButton'
import { getDagreLayoutedElements } from '../dagreAutoLayout'
import SubtreeInternalNode from './SubtreeInternalNode'
import SubtreeLeafNode from './SubtreeLeafNode'
import useSubtreeStore from '@/app/stores/subtree_store'
// TODO: Migrate to AI SDK 5 - temporarily disabled for build
// import { useChat } from '@ai-sdk/react'
import useIssueTreeStore from '@/app/stores/issuetree_store'
import { usePathname } from 'next/navigation'
import { SubtreeStatus } from '@prisma/client'
import { DocumentTree } from './DocumentTree'
import { Textarea } from '@/components/ui/textarea'
import mixpanel from '@/app/libs/mixpanel'
import { addSuffixAfterNumber } from '@/lib/utils'
import { useConversationStore } from '@/app/stores/conversation_store'
import { ConversationStatus } from '@prisma/client'
import { useSession } from 'next-auth/react'
import { SubtreeGenerateQuestionsRequestType } from '@/app/types/api'

interface SubtreeDialogProps {
  id: string
}

// Address this warning
// https://reactflow.dev/learn/troubleshooting#it-looks-like-you-have-created-a-new-nodetypes-or-edgetypes-object-if-this-wasnt
const nodeTypes = {
  default: SubtreeInternalNode,
  customLeafNode: SubtreeLeafNode,
}

const SubtreeDialog: React.FC<SubtreeDialogProps> = ({ id }) => {
  const issueTreeStore = useIssueTreeStore()
  const subtreeStore = useSubtreeStore()
  const conversationStore = useConversationStore()
  const { data: session } = useSession()
  // Get conversation Id from the path
  const pathname = usePathname()
  const conversationId = pathname.split('/').pop()

  const shouldDisable =
    conversationStore.getConversationStatus(conversationId || '') ===
      ConversationStatus.COMPLETED ||
    conversationStore.getConversationStatus(conversationId || '') ===
      ConversationStatus.EXAMPLE

  const { path, childs, peers } = findNeighbors(id, issueTreeStore.edges)

  const nodeMapping = nodesToDict(issueTreeStore.nodes)
  const markdown = constructMarkdown(path, childs, peers, nodeMapping, id)

  const displayMarkdown = constructDisplayMarkdown(
    nodeMapping,
    issueTreeStore.edges,
    id
  )

  const [textareaInputText, setTextareaInputText] = useState('')

  const handleInputChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTextareaInputText(event.target.value)
  }

  const requestData: SubtreeGenerateQuestionsRequestType = {
    issueTreeId: issueTreeStore.issueTreeId,
    conversationId: conversationId || '',
    userId: session?.user?.id || '',
    markdown,
    originalAskText: issueTreeStore.originalAskText,
    nodeMapping,
    id,
    childs,
    customDirective: textareaInputText,
  }

  // TODO: Migrate to AI SDK 5 - temporarily disabled for build
  const messages: any[] = []
  const append = () => {}
  const isLoading = false

  /* const { messages, append, isLoading } = useChat({
    api: '/api/subtree/generate_questions',
    body: requestData,
  }) */

  // Check if there exists a subtree
  const existSubtree = subtreeStore.subtrees[id]?.nodes.length > 0
  // Define button states based on loading state and existence of a subtree
  const canClickGenerateButton = !isLoading && !existSubtree
  const canClickManageButtons = !isLoading && existSubtree

  // Update the subtree when the messages from useChat is changing
  useEffect(() => {
    const lastMessage = messages[messages.length - 1]
    if (lastMessage && lastMessage.role === 'assistant') {
      const { nodes: parsedNodes, edges: parsedEdges } = markdownToReactFlow(
        addSuffixAfterNumber(lastMessage.content, '[EXAMPLE NUMBER]')
      )

      const layoutedElements = getDagreLayoutedElements(
        parsedNodes,
        parsedEdges
      )
      subtreeStore.setNodesAndEdges(
        issueTreeStore.issueTreeId,
        id,
        layoutedElements.nodes,
        layoutedElements.edges,
        lastMessage.content,
        false // Do not save to DB, onCompletion will do that
      )
    }
  }, [messages, id, issueTreeStore.issueTreeId, subtreeStore])

  // Function to close the dialog
  const closeDialog = () => subtreeStore.onClose()

  const clickMerge = () => {
    toast.success('Clicked merge')
    mixpanel.track('merge_subtree', {
      location: 'subtree_dialog',
      selected_node_id: id,
      selected_node_label: nodeMapping[id].label,
    })
    // 0. Compute the new state AND reshape the nodes and edges
    const [mergedNodes, mergedEdges] = mergeSubtreeIntoIssueTree(
      id,
      issueTreeStore.nodes,
      issueTreeStore.edges,
      subtreeStore.subtrees[id].nodes,
      subtreeStore.subtrees[id].edges
    )
    const layoutedElements = getDagreLayoutedElements(mergedNodes, mergedEdges)
    console.log('newLayoutedElements.edges', layoutedElements.edges)
    console.log('newLayoutedElements.nodes', layoutedElements.nodes)

    issueTreeStore.setNodesAndEdges(
      layoutedElements.nodes,
      layoutedElements.edges
    )

    // Update subtree store and mark status in DB
    subtreeStore.removeSubtree(
      issueTreeStore.issueTreeId,
      id,
      subtreeStore.subtrees[id].nodes,
      subtreeStore.subtrees[id].edges,
      SubtreeStatus.MERGED
    )

    // After 1 seconds close the dialog
    setTimeout(() => {
      closeDialog()
    }, 1000)
  }

  // Reset to the original state
  const clickReset = () => {
    toast.success('Resetting')
    mixpanel.track('reset_subtree', {
      location: 'subtree_dialog',
      selected_node_id: id,
      selected_node_label: nodeMapping[id].label,
    })

    const { nodes: parsedNodes, edges: parsedEdges } = markdownToReactFlow(
      subtreeStore.subtrees[id].generation_output
    )

    const layoutedElements = getDagreLayoutedElements(parsedNodes, parsedEdges)

    subtreeStore.setNodesAndEdges(
      issueTreeStore.issueTreeId,
      id,
      layoutedElements.nodes,
      layoutedElements.edges,
      subtreeStore.subtrees[id].generation_output,
      true
    )
  }

  const clickDelete = () => {
    toast.success('Deleting')
    mixpanel.track('delete_subtree', {
      location: 'subtree_dialog',
      selected_node_id: id,
      selected_node_label: nodeMapping[id].label,
    })
    // Update subtree store and mark status in DB
    subtreeStore.removeSubtree(
      issueTreeStore.issueTreeId,
      id,
      subtreeStore.subtrees[id].nodes,
      subtreeStore.subtrees[id].edges,
      SubtreeStatus.INACTIVE
    )
  }

  const clickGenerate = async () => {
    toast.success('Generating subtree')
    mixpanel.track('generate_subtree', {
      location: 'subtree_dialog',
      selected_node_id: id,
      selected_node_label: nodeMapping[id].label,
    })
    // await append({
    //   content: 'Kickstart the subtree tree generation',
    //   role: 'system',
    // })
  }

  function shouldNodeBeRemoved(
    node: Node,
    nodes: Node[],
    edges: Edge[]
  ): { removable: boolean; message: string } {
    const incomers = getIncomers(node, nodes, edges)
    if (incomers.length === 0) {
      // The node has no parent, should not be removed
      return {
        removable: false,
        message: `This node has no parent and cannot be removed.`,
      }
    }

    if (node.type === 'customLeafNode') {
      const parent = incomers[0] // Assuming a single parent for simplicity
      const outgoers = getOutgoers(parent, nodes, edges).filter(
        n => n.id !== node.id
      )
      // parent's outgoers excluding the selected node
      if (outgoers.length < 1) {
        // No other leaf siblings, should not be removed
        return {
          removable: false,
          message: `This node is the only leaf and cannot be removed.`,
        }
      }
    }

    return { removable: true, message: '' } // Node can be removed
  }

  const onNodesDelete = useCallback(
    (deletedNodes: Node[]) => {
      const nodesToRemove: Node[] = []
      const messagesToKeep: any[] = []

      deletedNodes.forEach(node => {
        const check = shouldNodeBeRemoved(
          node,
          subtreeStore.subtrees[id].nodes,
          subtreeStore.subtrees[id].edges
        )
        if (check.removable) {
          nodesToRemove.push(node)
        } else {
          messagesToKeep.push(check.message)
        }
      })

      // Display specialized errors for nodes that cannot be removed
      messagesToKeep.forEach(message => {
        toast.error(message)
      })

      if (nodesToRemove.length === 0) {
        return // Exit if no nodes can be removed
      }

      // Calculate new edges, excluding ones connected to removed nodes and adding connections between their incomers and outgoers
      const newEdges = subtreeStore.subtrees[id].edges
        .filter(
          edge =>
            !nodesToRemove.find(
              node => node.id === edge.source || node.id === edge.target
            )
        )
        .concat(
          ...nodesToRemove.flatMap(node => {
            const incomers = getIncomers(
              node,
              subtreeStore.subtrees[id].nodes,
              subtreeStore.subtrees[id].edges
            )
            const outgoers = getOutgoers(
              node,
              subtreeStore.subtrees[id].nodes,
              subtreeStore.subtrees[id].edges
            )
            return incomers.flatMap(incomer =>
              outgoers.map(outgoer => ({
                id: `e-${incomer.id}-${outgoer.id}`,
                source: incomer.id,
                target: outgoer.id,
              }))
            )
          })
        )

      const idsToRemove = nodesToRemove.map(node => node.id)
      const newNodes = subtreeStore.subtrees[id].nodes.filter(
        node => !idsToRemove.includes(node.id)
      )
      console.log(newNodes, newEdges)
      // Update the store with the new nodes and edges
      // We should save the change to DB
      subtreeStore.setNodesAndEdges(
        issueTreeStore.issueTreeId,
        id,
        newNodes,
        newEdges,
        subtreeStore.subtrees[id].generation_output,
        true
      )
    },
    [subtreeStore, id, issueTreeStore.issueTreeId]
  )

  const proOptions = { hideAttribution: true }

  return (
    <ReactFlowProvider>
      <Dialog
        open={subtreeStore.selectedSubtree?.isOpen}
        onOpenChange={subtreeStore.onClose}
      >
        <DialogContent className="lg:max-w-[1200px] sm:max-w-[825px] p-6 overflow-hidden">
          <div className="flex space-x-4">
            <div className="flex-1">
              <DialogTitle>
                Create subtree for: {nodeMapping[id].label}
              </DialogTitle>
              <br />
              <div className="max-h-32 overflow-y-auto p-2 bg-gray-100 rounded">
                <strong>Original ask:</strong> {issueTreeStore.originalAskText}
              </div>
              <Textarea
                placeholder="OPTIONAL: specific directions, eg: focus more on X, ignore Y, etc."
                rows={2}
                className="w-full p-2 mt-4 border rounded-md"
                disabled={!canClickGenerateButton || isLoading}
                onChange={handleInputChange}
              />
              <br />
              <DocumentTree markdownStructure={displayMarkdown} />
              <div className="flex flex-row justify-center mt-4 space-x-4">
                <HoverButton
                  onClick={clickGenerate}
                  buttonText="Generate subtree"
                  hoverMessage="Expand the selected node by generating subtree"
                  dialogProps={{ cannotUndo: false }}
                  isClickable={
                    canClickGenerateButton && !isLoading && !shouldDisable
                  }
                />
                <HoverButton
                  onClick={clickDelete}
                  buttonText="Delete subtree"
                  hoverMessage="Cannot UNDO!!! If you don't like existing subtree, you can remove it and regenerate"
                  dialogProps={{
                    cannotUndo: true,
                    dialogTitle: 'Delete subtree',
                    dialogDescription: 'This action cannot be undone.',
                  }}
                  isClickable={canClickManageButtons && !isLoading}
                />
                <HoverButton
                  onClick={clickReset}
                  buttonText="Reset subtree"
                  hoverMessage="Cannot UNDO!!! Reset it back to the initial version"
                  dialogProps={{
                    cannotUndo: true,
                    dialogTitle: 'Reset subtree to original version',
                    dialogDescription: 'This action cannot be undone.',
                  }}
                  isClickable={canClickManageButtons && !isLoading}
                />
              </div>
            </div>
            <div className="flex-1" style={{ height: '80vh' }}>
              {canClickManageButtons && !isLoading ? (
                <h1 className="text-lg font-semibold text-gray-700">
                  Select the node and press DELETE to prune the subtree 🌳
                </h1>
              ) : (
                <div className="h-8"></div> // Empty space as a placeholder
              )}

              <ReactFlow
                nodes={
                  subtreeStore.subtrees[id]
                    ? subtreeStore.subtrees[id].nodes
                    : []
                }
                edges={
                  subtreeStore.subtrees[id]
                    ? subtreeStore.subtrees[id].edges
                    : []
                }
                nodeTypes={nodeTypes}
                // onNodesChange={onNodesDelete}
                // onEdgesChange={onSubtreeEdgesChange}
                // onConnect={onSubtreeConnect}
                onNodesDelete={onNodesDelete}
                panOnScroll
                fitView
                fitViewOptions={{
                  padding: 5,
                  minZoom: 0.25,
                  maxZoom: 0.5,
                  duration: 1000,
                  // Based on the code in markdownToReactFlow, this will lead the root node
                  // If this doesn't exist [unlikely, it means things are broken]
                  // it will just show fitView, all nodes
                  nodes: subtreeStore.subtrees[id]
                    ? subtreeStore.subtrees[id].nodes.filter(
                        node => node.id === 'L1:1'
                      )
                    : [],
                }}
                attributionPosition="top-right"
                proOptions={proOptions}
              >
                {/* <MiniMap zoomable pannable /> */}
                <Controls />
                <Background color="#555" variant={BackgroundVariant.Dots} />
              </ReactFlow>
            </div>
          </div>
          <div className="flex flex-row justify-center mt-4 space-x-4">
            <HoverButton
              onClick={clickMerge}
              buttonText="Merge to main tree!"
              hoverMessage="Cannot UNDO!!! Merge this subtree to the main issue tree"
              dialogProps={{
                cannotUndo: true,
                dialogTitle: 'Merge subtree',
                dialogDescription: 'This action cannot be undone.',
              }}
              isClickable={
                canClickManageButtons && !isLoading && !shouldDisable
              }
            />
          </div>
        </DialogContent>
      </Dialog>
    </ReactFlowProvider>
  )
}

export default SubtreeDialog
