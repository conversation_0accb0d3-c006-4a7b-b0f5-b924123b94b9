'use client'

import React from 'react'
import { useTabStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore'
import { Loader2, GitMerge, CircleDot } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { toast } from 'react-hot-toast'

/**
 * ReactFlow export buttons for use in header/tabs
 * Provides two download options: hierarchical (linear) and circular (radial) layouts
 * Triggers download via custom event to communicate with ReactFlow component
 */
const FlowExportButton: React.FC = () => {
  const [isDownloadingHierarchical, setIsDownloadingHierarchical] =
    React.useState<boolean>(false)
  const [isDownloadingCircular, setIsDownloadingCircular] =
    React.useState<boolean>(false)

  // Track diagram (nodes + viewport) readiness via 'reactflow-diagram-ready' event
  const [isDiagramReady, setIsDiagramReady] = React.useState<boolean>(false)

  // Access tab state to programmatically switch to ReactFlow view when needed
  const activeTabId = useTabStore(state => state.activeTabId)
  const setActiveTab = useTabStore(state => state.setActiveTab)

  // Listen for global readiness event once component mounts
  React.useEffect(() => {
    const handleReady = () => setIsDiagramReady(true)
    window.addEventListener('reactflow-diagram-ready', handleReady)
    return () =>
      window.removeEventListener('reactflow-diagram-ready', handleReady)
  }, [])

  const downloadImage = React.useCallback(
    (layoutMode: 'linear' | 'radial') => {
      const triggerDownload = () => {
        // Dispatch custom event for ReactFlow to handle
        if (typeof toast.dismiss === 'function') {
          toast.dismiss()
        }

        const downloadEvent = new CustomEvent('reactflow-download', {
          detail: {
            layoutMode,
            setIsDownloading:
              layoutMode === 'linear'
                ? setIsDownloadingHierarchical
                : setIsDownloadingCircular,
          },
        })
        window.dispatchEvent(downloadEvent)
      }

      const initiateDownloadAfterReady = () => {
        if (isDiagramReady || document.querySelector('.react-flow')) {
          triggerDownload()
          return
        }

        const timeoutId = setTimeout(() => {
          if (typeof toast.dismiss === 'function') {
            toast.dismiss()
          }
          toast.error(
            'Flow diagram is still loading. Please try again in a moment.'
          )
          window.removeEventListener('reactflow-diagram-ready', onceReady)
        }, 4000)

        function onceReady() {
          clearTimeout(timeoutId)
          window.removeEventListener('reactflow-diagram-ready', onceReady)
          triggerDownload()
        }

        window.addEventListener('reactflow-diagram-ready', onceReady)
      }

      if (activeTabId !== 'main') {
        setActiveTab('main')
        // Wait for readiness before triggering download
        initiateDownloadAfterReady()
      } else {
        initiateDownloadAfterReady()
      }
    },
    [
      activeTabId,
      setActiveTab,
      setIsDownloadingCircular,
      setIsDownloadingHierarchical,
      isDiagramReady,
    ]
  )

  // Listen for download status updates
  React.useEffect(() => {
    const handleDownloadStart = (event: CustomEvent) => {
      const layoutMode = event.detail?.layoutMode
      if (layoutMode === 'linear') {
        setIsDownloadingHierarchical(true)
      } else if (layoutMode === 'radial') {
        setIsDownloadingCircular(true)
      }
    }
    const handleDownloadEnd = (event: CustomEvent) => {
      const layoutMode = event.detail?.layoutMode
      if (layoutMode === 'linear') {
        setIsDownloadingHierarchical(false)
      } else if (layoutMode === 'radial') {
        setIsDownloadingCircular(false)
      }
    }

    window.addEventListener(
      'reactflow-download-start',
      handleDownloadStart as EventListener
    )
    window.addEventListener(
      'reactflow-download-end',
      handleDownloadEnd as EventListener
    )

    return () => {
      window.removeEventListener(
        'reactflow-download-start',
        handleDownloadStart as EventListener
      )
      window.removeEventListener(
        'reactflow-download-end',
        handleDownloadEnd as EventListener
      )
    }
  }, [])

  return (
    <div className="space-y-2">
      <Button
        onClick={() => downloadImage('linear')}
        disabled={isDownloadingHierarchical || isDownloadingCircular}
        variant="default"
        className="w-full justify-start"
      >
        {isDownloadingHierarchical ? (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        ) : (
          <GitMerge className="mr-2 h-4 w-4" />
        )}
        {isDownloadingHierarchical
          ? 'Downloading...'
          : 'Download Hierarchical Flow'}
      </Button>

      <Button
        onClick={() => downloadImage('radial')}
        disabled={isDownloadingHierarchical || isDownloadingCircular}
        variant="outline"
        className="w-full justify-start"
      >
        {isDownloadingCircular ? (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        ) : (
          <CircleDot className="mr-2 h-4 w-4" />
        )}
        {isDownloadingCircular ? 'Downloading...' : 'Download Circular Flow'}
      </Button>
    </div>
  )
}

// Memoize to prevent unnecessary re-renders when no props change
export const MemoizedFlowExportButton = React.memo(FlowExportButton)
export { MemoizedFlowExportButton as FlowExportButton }
