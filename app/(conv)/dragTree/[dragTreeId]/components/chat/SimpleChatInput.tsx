'use client'

import React, { useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { FiSend } from 'react-icons/fi'
import { CONTEXT_CONFIG } from '@/app/api/aipane/assistant/config'

type SimpleChatInputProps = {
  input: string
  onInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void
  onSubmit: (e: React.FormEvent) => void
  disabled?: boolean
  isLoading?: boolean
  placeholder?: string
  className?: string
}

export default function SimpleChatInput({
  input,
  onInputChange,
  onSubmit,
  disabled = false,
  isLoading = false,
  placeholder = 'Message AI Assistant...',
  className = '',
}: SimpleChatInputProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const isOverLimit = input.length > CONTEXT_CONFIG.MAX_INPUT_CHARS

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current
    if (textarea) {
      textarea.style.height = 'auto'
      textarea.style.height = `${Math.min(textarea.scrollHeight, 128)}px`
    }
  }, [input])

  // Handle Enter key (submit on Enter, new line on Shift+Enter)
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      if (!disabled && !isLoading && input.trim() && !isOverLimit) {
        onSubmit(e as any)
      }
    }
  }

  const canSubmit = !disabled && !isLoading && input.trim() && !isOverLimit

  return (
    <div className={`border-t border-gray-200 bg-white ${className}`}>
      <div className="max-w-4xl mx-auto p-4">
        <form onSubmit={onSubmit} className="relative">
          <div className="relative flex items-end bg-gray-50 rounded-xl border border-gray-200 focus-within:border-blue-500 transition-colors">
            <Textarea
              ref={textareaRef}
              value={input}
              onChange={onInputChange}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              disabled={disabled}
              className={`flex-1 min-h-[44px] max-h-32 resize-none border-0 bg-transparent px-4 py-3 text-sm placeholder:text-gray-500 focus:outline-none focus:ring-0 ${
                isOverLimit ? 'text-red-600' : ''
              }`}
              rows={1}
            />
            <Button
              type="submit"
              size="sm"
              disabled={!canSubmit}
              className="m-2 h-8 w-8 p-0 rounded-lg bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 transition-colors"
            >
              <FiSend className="h-4 w-4" />
            </Button>
          </div>

          {/* Character counter and shortcuts */}
          <div className="flex justify-between items-center mt-2 text-xs text-gray-500">
            <div className="flex items-center gap-4">
              {isOverLimit ? (
                <span className="text-red-600 font-medium">
                  ⚠️ Message too long. Limit: {CONTEXT_CONFIG.MAX_INPUT_CHARS}{' '}
                  chars.
                </span>
              ) : (
                <span>Press Enter to send, Shift+Enter for new line</span>
              )}
            </div>
            <span className={isOverLimit ? 'text-red-600 font-semibold' : ''}>
              {input.length}/{CONTEXT_CONFIG.MAX_INPUT_CHARS}
            </span>
          </div>
        </form>
      </div>
    </div>
  )
}
