import React, { useState, useMemo } from 'react'
import { toast } from 'react-hot-toast'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { Search } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { NodeContentType } from '@/app/(conv)/dragTree/[dragTreeId]/constants/node-types'
import { shallow } from 'zustand/shallow'
import { DragTreeNodeContentStatus } from '@prisma/client'
import { createExternalToolPrompt } from '@/app/api/dragtree/shared/research-prompts'
import {
  RESEARCH_OPTIONS_CONFIG,
  type ResearchOptionConfig,
} from '@/app/(conv)/dragTree/constants'
import {
  getLanguageName,
  type SupportedLanguageCode,
} from '@/app/(conv)/screening/constants/languages'

type QuickResearchDropdownButtonProps = {
  questionText: string
  questionNodeId: string
  variant?: 'reactflow' | 'treeview'
  className?: string
  onMouseEnter?: () => void
  onMouseLeave?: () => void
}

/**
 * QuickResearchDropdownButton - Icon-only button with dropdown menu for research options
 *
 * Purpose:
 * - Shows a search icon with dropdown containing research options
 * - Internal research: Creates content record that triggers QuickResearchPreviewButton
 * - External research: Opens external services with formatted prompts
 *
 * Behavior:
 * - When internal research is selected: Creates content record → QuickResearchPreviewButton detects and streams
 * - When external research is selected: Opens external service in new tab
 *
 * This is the "trigger" button - it doesn't handle streaming itself.
 */
const QuickResearchDropdownButton: React.FC<
  QuickResearchDropdownButtonProps
> = ({
  questionText,
  questionNodeId,
  variant = 'treeview',
  className = '',
  onMouseEnter,
  onMouseLeave,
}) => {
  const [dropdownOpen, setDropdownOpen] = useState(false)
  const [alertOpen, setAlertOpen] = useState(false)
  const [pendingUrl, setPendingUrl] = useState('')
  const [pendingLabel, setPendingLabel] = useState('')

  // Get what we need from stores
  const nodeContentMap = useDragTreeStore(
    state => state.nodeContent.get(questionNodeId),
    shallow
  )
  const getNodePath = useDragTreeStore(state => state.getNodePath)
  const screeningQuestion = useDragTreeStore(state => state.screeningQuestion)
  const markNodeAsInterested = useDragTreeStore(
    state => state.markNodeAsInterested
  )
  const addNodeContent = useDragTreeStore(state => state.addNodeContent)
  const preferredLanguage = useDragTreeStore(state => state.preferredLanguage)

  // Memoize derived data from nodeContentMap
  const { hasExistingContent, isStreaming } = useMemo(() => {
    const exist = nodeContentMap && nodeContentMap.size > 0
    const streaming = Array.from(nodeContentMap?.values() || []).some(
      content => content.status === DragTreeNodeContentStatus.PROCESSING
    )
    return { hasExistingContent: exist, isStreaming: streaming }
  }, [nodeContentMap])

  const handleOptionClick = async (option: ResearchOptionConfig) => {
    setDropdownOpen(false)

    if (option.id === 'clarify') {
      // Internal research - create content record that QuickResearchPreviewButton will detect
      try {
        const fullPath = getNodePath(questionNodeId) || questionText

        const createPayload = {
          dragTreeNodeId: questionNodeId,
          questionText: fullPath,
          researchType: 'QUICK_RESEARCH',
        }

        const createResponse = await fetch('/api/dragtree/research_create', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(createPayload),
        })

        if (!createResponse.ok) {
          throw new Error('Failed to create research content record')
        }

        const createResult = await createResponse.json()
        const contentId = createResult.data.contentId

        // Add to store to trigger QuickResearchPreviewButton's research lifecycle
        addNodeContent(questionNodeId, {
          contentId,
          contentType: NodeContentType.QUICK_RESEARCH,
          contentVersion: 'v1',
          status: DragTreeNodeContentStatus.INITIALIZED,
          contentText: '',
          metadata: {
            questionText: fullPath,
            originalQuestion: fullPath,
          },
        })

        toast.success('Research started!')
      } catch (error) {
        toast.error('Failed to start research')
        console.error('Research failed:', error)
      }
    } else {
      // External service - use properly formatted prompt with context
      // Mark the node as interested when user performs any research action
      markNodeAsInterested(questionNodeId)

      const fullPath = getNodePath(questionNodeId) || questionText

      // We include language preference in the simplified prompt
      const language = preferredLanguage
        ? getLanguageName(preferredLanguage as SupportedLanguageCode)
        : 'English'
      const formattedPrompt = createExternalToolPrompt(
        fullPath,
        screeningQuestion,
        language
      )

      const query = encodeURIComponent(formattedPrompt)
      const url = option.urlPattern.replace('{query}', query)

      setPendingUrl(url)
      setPendingLabel(option.label)
      setAlertOpen(true)
    }
  }

  // Confirm external service
  const handleConfirmExternal = () => {
    window.open(pendingUrl, '_blank', 'noopener,noreferrer')
    setAlertOpen(false)
    setPendingUrl('')
    setPendingLabel('')
    toast.success(`Opened ${pendingLabel}`)
  }

  // Button styling
  const buttonClasses = cn('h-6 w-6 p-0 transition-all duration-200', {
    'hover:bg-blue-100 hover:scale-105': variant === 'reactflow',
    'rounded-full bg-gradient-to-r from-purple-50 to-violet-50 border-2 border-purple-200 shadow-sm hover:from-purple-100 hover:to-violet-100 hover:border-purple-300 hover:shadow-md hover:scale-105':
      variant === 'treeview',
  })

  const iconClasses = cn('h-3 w-3', {
    [className || 'text-slate-600 hover:text-slate-700']:
      variant === 'reactflow',
    'text-purple-600': variant === 'treeview',
    'text-blue-500 animate-pulse': isStreaming,
  })

  return (
    <>
      <div
        className="flex items-center gap-1"
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
      >
        <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
          <DropdownMenuTrigger asChild>
            <Button
              variant={variant === 'reactflow' ? 'ghost' : 'outline'}
              size={variant === 'reactflow' ? 'sm' : 'icon'}
              className={cn(buttonClasses, {
                'animate-pulse': isStreaming,
              })}
              onClick={e => {
                e.preventDefault()
                e.stopPropagation()
              }}
              onMouseDown={e => e.stopPropagation()}
              onMouseUp={e => e.stopPropagation()}
              title={
                isStreaming ? 'Research in progress...' : 'Research Options'
              }
            >
              <Search className={iconClasses} />
            </Button>
          </DropdownMenuTrigger>

          <DropdownMenuContent
            align="start"
            side="bottom"
            className="w-52 rounded-xl border-0 shadow-xl bg-white/95 backdrop-blur-sm z-50"
            sideOffset={4}
          >
            {RESEARCH_OPTIONS_CONFIG.map((option, index) => (
              <React.Fragment key={option.id}>
                {/* Separator before external options */}
                {index === 1 && (
                  <div className="flex items-center gap-2 px-2 py-1.5 text-xs text-gray-500">
                    <div className="flex-1 h-px bg-gray-200" />
                    <span className="px-2 bg-gray-100 rounded-full">
                      External Tools
                    </span>
                    <div className="flex-1 h-px bg-gray-200" />
                  </div>
                )}

                <DropdownMenuItem
                  onClick={e => {
                    e.preventDefault()
                    e.stopPropagation()
                    handleOptionClick(option)
                  }}
                  className={cn('cursor-pointer rounded-lg', {
                    'opacity-50 cursor-not-allowed':
                      option.id === 'clarify' &&
                      (hasExistingContent || isStreaming),
                  })}
                  disabled={
                    option.id === 'clarify' &&
                    (hasExistingContent || isStreaming)
                  }
                >
                  <span>{option.label}</span>

                  {option.id === 'clarify' && hasExistingContent && (
                    <span className="ml-auto text-xs text-gray-400">
                      ✓ Done
                    </span>
                  )}
                  {option.id === 'clarify' && isStreaming && (
                    <span className="ml-auto text-xs text-blue-500 animate-pulse">
                      ⏳ Researching...
                    </span>
                  )}
                </DropdownMenuItem>
              </React.Fragment>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* External service confirmation dialog */}
      <AlertDialog open={alertOpen} onOpenChange={setAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Continue to {pendingLabel}?</AlertDialogTitle>
            <AlertDialogDescription className="text-gray-600">
              You&apos;re about to open an external site. While it&apos;s fine
              to do your own research, please note that any context or results
              from external sites are <strong>NOT</strong> automatically synced
              back here.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => {
                setAlertOpen(false)
                setPendingUrl('')
                setPendingLabel('')
              }}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmExternal}>
              Continue
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

export default React.memo(QuickResearchDropdownButton)
