'use client'

import React, { useState, useRef, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { FiX, FiFile, FiImage, FiChevronDown } from 'react-icons/fi'
import {
  useAiPaneStore,
  MAX_TOKENS,
} from '@/app/(conv)/dragTree/[dragTreeId]/stores/useAiPaneStore'
import { prompts } from '@/app/(conv)/conversations/[conversationId]/issue_tree/NotebookDialog/promptClone'
import { encode } from 'gpt-tokenizer'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import LanguageSelector from '@/app/components/ui/LanguageSelector'
import {
  type SupportedLanguageCode,
  DEFAULT_LANGUAGE,
} from '@/app/constants/languages'

type SettingsSectionProps = {
  dragTreeId: string
}

// Use existing notebook prompts from the system
const generateTemplates = prompts.map((prompt, _index) => ({
  id: prompt.title.toLowerCase().replace(/\s+/g, '-'),
  name: prompt.title,
  template: prompt.description,
}))

const TemplateSelector: React.FC<{
  onSelectTemplate: (template: string, templateName: string) => void
  isCollapsed: boolean
  onToggleCollapse: () => void
  onUseCustomPrompt: () => void
}> = ({
  onSelectTemplate,
  isCollapsed,
  onToggleCollapse,
  onUseCustomPrompt,
}) => {
  return (
    <div className="space-y-2">
      {/* Header with collapse toggle */}
      <div className="flex items-center justify-between">
        <button
          onClick={onToggleCollapse}
          className="flex items-center space-x-2 text-xs text-gray-600 hover:text-gray-800 transition-colors"
        >
          <FiChevronDown
            className={cn(
              'w-3 h-3 transition-transform',
              isCollapsed && '-rotate-90'
            )}
          />
          <span>Quick Templates</span>
        </button>
        <Button
          onClick={onUseCustomPrompt}
          variant="ghost"
          size="sm"
          className="h-6 px-2 text-xs text-blue-600 hover:text-blue-800"
        >
          Use custom prompt
        </Button>
      </div>

      {/* Collapsible template grid */}
      {!isCollapsed && (
        <div className="max-h-[200px] overflow-y-auto">
          <div className="grid grid-cols-2 gap-2 pr-2">
            {generateTemplates.map(template => (
              <Button
                key={template.id}
                variant="outline"
                size="sm"
                onClick={() =>
                  onSelectTemplate(template.template, template.name)
                }
                className="h-8 text-xs justify-start"
                title={template.template.substring(0, 100) + '...'}
              >
                <span className="truncate">{template.name}</span>
              </Button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

const SettingsSection: React.FC<SettingsSectionProps> = ({
  dragTreeId: _dragTreeId,
}) => {
  const settingsType = useAiPaneStore(state => state.settings.type)
  const settingsPrompt = useAiPaneStore(state => state.settings.prompt)
  const settingsFiles = useAiPaneStore(state => state.settings.files)
  const settingsImages = useAiPaneStore(state => state.settings.images)
  const settingsLanguage = useAiPaneStore(state => state.settings.language)
  const setPrompt = useAiPaneStore(state => state.setPrompt)
  const setPromptTemplate = useAiPaneStore(state => state.setPromptTemplate)
  const setLanguage = useAiPaneStore(state => state.setLanguage)
  const addFile = useAiPaneStore(state => state.addFile)
  const removeFile = useAiPaneStore(state => state.removeFile)
  const addImage = useAiPaneStore(state => state.addImage)
  const removeImage = useAiPaneStore(state => state.removeImage)
  const contextTokenCount = useAiPaneStore(state => state.contextTokenCount)

  // Get drag tree's preferred language as default
  // This provides the fallback language when no override is specified for this generation
  const dragTreePreferredLanguage = useDragTreeStore(
    state => state.preferredLanguage
  )

  const [templatesCollapsed, setTemplatesCollapsed] = useState(false)
  const [promptTokenCount, setPromptTokenCount] = useState(0)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const imageInputRef = useRef<HTMLInputElement>(null)

  // Get the effective language (override or drag tree default)
  // Priority: 1) User override for this generation, 2) Drag tree preference, 3) Default (en)
  // This ensures the dropdown shows the correct default while allowing per-generation overrides
  const effectiveLanguage =
    settingsLanguage ||
    (dragTreePreferredLanguage as SupportedLanguageCode) ||
    DEFAULT_LANGUAGE

  // Calculate token count for the prompt whenever it changes
  useEffect(() => {
    try {
      const tokens = encode(settingsPrompt)
      setPromptTokenCount(tokens.length)
    } catch (error) {
      console.error('Error calculating prompt token count:', error)
      setPromptTokenCount(0) // Reset on error
    }
  }, [settingsPrompt])

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files) return

    Array.from(files).forEach(file => {
      addFile(file)
    })

    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files) return

    Array.from(files).forEach(file => {
      addImage(file)
    })

    if (imageInputRef.current) {
      imageInputRef.current.value = ''
    }
  }

  const handleTemplateSelect = (template: string, templateName: string) => {
    setPrompt(template)
    setPromptTemplate(templateName) // Track the selected template name
    setTemplatesCollapsed(true) // Auto-collapse after selection
  }

  const handleUseCustomPrompt = () => {
    setTemplatesCollapsed(true) // Collapse templates
    setPrompt('') // Clear any existing prompt
    setPromptTemplate('Custom Prompt') // Set template name for custom prompts
    // Focus will be automatically on textarea
  }

  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      {/* Header - Compact */}
      <div className="px-3 py-2">
        <h3 className="text-xs font-medium text-gray-700 mb-2">
          {settingsType === 'generate' ? 'Generation Prompt' : 'Chat Message'}
        </h3>

        {/* Template selector for generate mode */}
        {settingsType === 'generate' && (
          <div className="mb-2">
            <TemplateSelector
              onSelectTemplate={handleTemplateSelect}
              isCollapsed={templatesCollapsed}
              onToggleCollapse={() =>
                setTemplatesCollapsed(!templatesCollapsed)
              }
              onUseCustomPrompt={handleUseCustomPrompt}
            />
          </div>
        )}
      </div>

      {/* Chat area - More space */}
      <div className="flex-1 flex flex-col px-3 pb-2 overflow-hidden">
        <div className="relative flex-1 flex flex-col">
          <Textarea
            value={settingsPrompt}
            onChange={e => setPrompt(e.target.value)}
            placeholder={
              settingsType === 'generate'
                ? 'Describe what you want to generate using the selected context...'
                : 'Start your conversation with the AI assistant...'
            }
            className={cn(
              'flex-1 resize-none text-sm min-h-[120px] transition-all',
              settingsPrompt.length === 0 && 'border-dashed border-gray-300',
              settingsPrompt.length > 0 && 'border-solid border-gray-200'
            )}
          />

          {/* Visual hints for empty textarea */}
          {settingsPrompt.length === 0 && (
            <div className="absolute inset-x-3 bottom-3 pointer-events-none">
              <div className="text-xs text-gray-400 bg-white px-2 py-1 rounded shadow-sm border border-gray-100">
                💡{' '}
                {settingsType === 'generate'
                  ? 'Try selecting a quick template above or write your own prompt'
                  : 'Ask questions about your selected research content'}
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-between items-center mt-2 text-xs text-gray-500">
          <span>
            {settingsPrompt.length} characters
            <span className="text-gray-400 mx-2">•</span>
            <span className="font-medium text-green-600">
              {promptTokenCount} tokens
            </span>
          </span>

          {/* Language Dropdown - positioned at the right */}
          <div className="flex-shrink-0">
            <LanguageSelector
              value={effectiveLanguage}
              onChange={setLanguage}
              compact={true}
              dropdownDirection="up"
              showDisclaimer={true}
            />
          </div>
        </div>

        {promptTokenCount + contextTokenCount > MAX_TOKENS && (
          <div className="mt-2 text-xs text-red-600 font-medium">
            🚨 Total token count is high. Please shorten your prompt or reduce
            selected context to enable generation.
          </div>
        )}
      </div>

      {/* Controls below textarea - Compact */}
      <div className="px-3 py-2 border-t border-gray-100 space-y-2">
        {/* File attachments display */}
        {(settingsFiles.length > 0 || settingsImages.length > 0) && (
          <div className="flex flex-wrap gap-1">
            {settingsFiles.map((file, index) => (
              <div
                key={index}
                className="flex items-center space-x-1 bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
              >
                <FiFile className="w-3 h-3" />
                <span className="max-w-[100px] truncate">{file.name}</span>
                <button
                  onClick={() => removeFile(index)}
                  className="hover:bg-blue-100 rounded"
                >
                  <FiX className="w-3 h-3" />
                </button>
              </div>
            ))}
            {settingsImages.map((image, index) => (
              <div
                key={index}
                className="flex items-center space-x-1 bg-purple-50 text-purple-700 px-2 py-1 rounded text-xs"
              >
                <FiImage className="w-3 h-3" />
                <span className="max-w-[100px] truncate">{image.name}</span>
                <button
                  onClick={() => removeImage(index)}
                  className="hover:bg-purple-100 rounded"
                >
                  <FiX className="w-3 h-3" />
                </button>
              </div>
            ))}
          </div>
        )}

        {/* Controls row */}
        <div className="flex items-center justify-between">
          {/* Left side - Model selector - Smaller */}
          <div />

          {/* API toggle removed – now controlled globally from header */}

          {/* Right side - Reserved for future controls */}
          <div className="flex items-center space-x-1">
            {/* File/Image upload controls removed - not currently supported */}
          </div>
        </div>
      </div>

      {/* Hidden file inputs */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept=".txt,.pdf,.doc,.docx,.md"
        onChange={handleFileUpload}
        className="hidden"
      />

      <input
        ref={imageInputRef}
        type="file"
        multiple
        accept="image/*"
        onChange={handleImageUpload}
        className="hidden"
      />
    </div>
  )
}

export default React.memo(SettingsSection)
