'use client'

import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  Suspense,
  lazy,
} from 'react'
import { Textarea } from '@/components/ui/textarea'
import { motion, AnimatePresence } from 'framer-motion'
import debounce from 'lodash/debounce'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { shallow } from 'zustand/shallow'
import { cn } from '@/lib/utils'
import { JSONContent } from '@tiptap/react'
import { Loader2 } from 'lucide-react'

// Dynamically import TipTap components to reduce initial bundle size
// This saves ~200KB+ from initial bundle since TipTap is only loaded when needed
const TiptapQuickResearchEditor = lazy(
  () => import('@/app/components/editor/TiptapQuickResearchEditor')
)
const TiptapTabEditor = lazy(
  () => import('@/app/components/editor/TiptapTabEditor')
)

// Loading skeleton for TipTap editors
const TiptapSkeleton: React.FC = () => (
  <div className="w-full min-h-[180px] border border-gray-200 rounded-md bg-gray-50 flex items-center justify-center">
    <div className="text-center">
      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto mb-2"></div>
      <p className="text-gray-600 text-sm">Loading editor...</p>
    </div>
  </div>
)

export type EditorType = 'textarea' | 'tiptap' | 'markdown'
export type EditorVariant = 'default' | 'compact' | 'fullscreen'

export interface ResearchEditorProps {
  nodeId: string
  contentId: string
  className?: string
  placeholder?: string
  variant?: EditorVariant
  editorType?: EditorType
  showStatusFooter?: boolean
  onContentChange?: (content: string) => void
}

/**
 * Reusable research content editor component
 * Can be used in both main research display and tab views
 * Supports different variants and customization options
 */
export const ResearchEditor: React.FC<ResearchEditorProps> = ({
  nodeId,
  contentId,
  className,
  placeholder = 'Research content will appear here...',
  variant = 'default',
  editorType = 'textarea',
  showStatusFooter = true,
  onContentChange,
}) => {
  const updateNodeContent = useDragTreeStore(state => state.updateNodeContent)

  const storedNodeContent = useDragTreeStore(
    state => state.getNodeContent(nodeId)?.get(contentId),
    shallow
  )

  // Fetch function and fetching status
  const fetchNodeContentFn = useDragTreeStore(state => state.fetchNodeContent)
  const isContentFetching = useDragTreeStore(
    state => state.isContentFetching(contentId),
    shallow
  )
  const storeContentText = storedNodeContent?.contentText || ''

  // ---------------------------------------------------------------------------
  // Lazy-load research content when the editor is rendered in a tab.
  // This ensures that refreshing the page with an open Quick Research tab
  // properly loads the content even if it has not been viewed via the preview
  // button. We initiate a fetch when:
  //   1) There is no stored content OR
  //   2) Stored content is empty/whitespace.
  // The DragTreeStore handles de-duplication, so repeated calls are safe.
  // ---------------------------------------------------------------------------
  useEffect(() => {
    if (
      (!storedNodeContent || storeContentText.trim().length === 0) &&
      !isContentFetching
    ) {
      fetchNodeContentFn(nodeId, contentId)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    storedNodeContent,
    storeContentText,
    nodeId,
    contentId,
    isContentFetching,
  ])

  // Local state for the textarea to handle real-time editing
  const [localContent, setLocalContent] = useState(storeContentText)
  const [isSaving, setIsSaving] = useState(false)
  const [justSaved, setJustSaved] = useState(false)

  // Refs for timeouts
  const justSavedTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Update local content when store content changes.
  // Avoid clearing the editor if the store temporarily resets to an empty string
  // (e.g., during route transitions) while we still have displayed content.
  useEffect(() => {
    if (
      storeContentText.trim().length === 0 &&
      localContent.trim().length > 0
    ) {
      // Skip the update to prevent a brief flicker.
      return
    }
    setLocalContent(storeContentText)
  }, [storeContentText])

  // Debounced save function with status indicators
  const debouncedSave = useCallback(
    debounce((newContentText: string) => {
      updateNodeContent(nodeId, contentId, { contentText: newContentText })
      setIsSaving(false)
      setJustSaved(true)

      if (justSavedTimeoutRef.current) clearTimeout(justSavedTimeoutRef.current)
      justSavedTimeoutRef.current = setTimeout(() => {
        setJustSaved(false)
      }, 2000)
    }, 1000),
    [nodeId, contentId, updateNodeContent]
  )

  const handleContentChange = (
    event: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const newContent = event.target.value
    setLocalContent(newContent)
    setIsSaving(true)
    debouncedSave(newContent)

    // Call external change handler if provided
    onContentChange?.(newContent)
  }

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (justSavedTimeoutRef.current) clearTimeout(justSavedTimeoutRef.current)
    }
  }, [])

  // Get variant-specific styles
  const getVariantStyles = () => {
    switch (variant) {
      case 'compact':
        return 'min-h-[120px] text-sm'
      case 'fullscreen':
        return 'text-sm'
      default:
        return 'min-h-[200px] text-sm'
    }
  }

  // Get container styles for fullscreen mode
  const getContainerStyles = () => {
    if (variant === 'fullscreen') {
      return 'h-full flex flex-col'
    }
    return ''
  }

  // Render loading state when content is being fetched
  const renderLoadingState = () => (
    <div className="flex items-center justify-center h-32 text-sm text-gray-400">
      <Loader2 className="h-4 w-4 animate-spin mr-2" />
      Loading research content...
    </div>
  )

  // Render the appropriate editor based on type
  const renderEditor = () => {
    // Show loading state when content is being fetched and no content exists yet
    if (
      isContentFetching &&
      (!storedNodeContent || storeContentText.trim().length === 0)
    ) {
      return renderLoadingState()
    }

    switch (editorType) {
      case 'tiptap':
        if (variant === 'fullscreen') {
          return (
            <Suspense fallback={<TiptapSkeleton />}>
              <TiptapTabEditor
                content={localContent || ''}
                onContentChange={(newContent: string) => {
                  setLocalContent(newContent)
                  setIsSaving(true)
                  debouncedSave(newContent)
                  onContentChange?.(newContent)
                }}
                onJSONChange={(jsonContent: JSONContent) => {
                  const jsonString = JSON.stringify(jsonContent)
                  setLocalContent(jsonString)
                  setIsSaving(true)
                  debouncedSave(jsonString)
                  onContentChange?.(jsonString)
                }}
                isReadOnly={false}
                isStreaming={false}
                placeholder={placeholder}
                debounceMs={1000}
                autoFocus={false}
              />
            </Suspense>
          )
        }
        return (
          <Suspense fallback={<TiptapSkeleton />}>
            <TiptapQuickResearchEditor
              content={localContent || ''}
              onContentChange={(newContent: string) => {
                setLocalContent(newContent)
                setIsSaving(true)
                debouncedSave(newContent)
                onContentChange?.(newContent)
              }}
              onJSONChange={(jsonContent: JSONContent) => {
                const jsonString = JSON.stringify(jsonContent)
                setLocalContent(jsonString)
                setIsSaving(true)
                debouncedSave(jsonString)
                onContentChange?.(jsonString)
              }}
              isReadOnly={false}
              isStreaming={false}
              placeholder={placeholder}
              className={cn(
                variant === 'compact' ? 'min-h-[120px]' : 'min-h-[180px]'
              )}
              showBubbleMenu={variant !== 'compact'}
              debounceMs={1000}
              autoFocus={false}
              compact={variant === 'compact'}
            />
          </Suspense>
        )
      case 'markdown':
        // Future: Markdown editor implementation
        return (
          <div
            className={cn(
              'w-full border border-slate-300 rounded-md p-3',
              getContainerStyles()
            )}
          >
            <div className="text-slate-500 text-sm mb-2">
              Markdown editor (coming soon)
            </div>
            <Textarea
              value={localContent}
              onChange={handleContentChange}
              className={cn(
                'w-full resize-none border-none focus:ring-0 focus:outline-none bg-transparent',
                getVariantStyles()
              )}
              placeholder={placeholder}
            />
          </div>
        )
      default: // textarea
        return (
          <Textarea
            value={localContent}
            onChange={handleContentChange}
            className={cn(
              'w-full resize-none border-slate-300 focus:border-blue-400 focus:ring-1 focus:ring-blue-200 transition-colors',
              getVariantStyles(),
              variant === 'fullscreen' && 'h-full'
            )}
            placeholder={placeholder}
            style={variant === 'fullscreen' ? { height: '100%' } : {}}
          />
        )
    }
  }

  return (
    <div
      className={cn('w-full flex flex-col', getContainerStyles(), className)}
    >
      <div
        className={cn(
          'relative',
          variant === 'fullscreen' ? 'flex-1 overflow-hidden min-h-0' : ''
        )}
      >
        {renderEditor()}
      </div>

      {/* Status Footer */}
      {showStatusFooter && editorType !== 'tiptap' && (
        <div
          className={cn(
            'flex items-center justify-end text-xs text-gray-400 h-5 pr-2',
            variant === 'fullscreen' ? 'mt-2 flex-shrink-0' : 'mt-2'
          )}
        >
          <AnimatePresence mode="wait">
            {isSaving ? (
              <motion.div
                key="saving"
                initial={{ opacity: 0, y: 5 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -5 }}
                style={{ display: 'flex', alignItems: 'center' }}
              >
                <span className="mr-1.5 w-1.5 h-1.5 rounded-full bg-amber-500 animate-pulse"></span>
                Saving...
              </motion.div>
            ) : justSaved ? (
              <motion.div
                key="saved"
                initial={{ opacity: 0, y: 5 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -5 }}
                style={{ color: '#10b981' }}
              >
                Saved
              </motion.div>
            ) : localContent ? (
              <motion.div
                key="edit"
                initial={{ opacity: 0, y: 5 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -5 }}
              >
                Ready to edit
              </motion.div>
            ) : null}
          </AnimatePresence>
        </div>
      )}
    </div>
  )
}
