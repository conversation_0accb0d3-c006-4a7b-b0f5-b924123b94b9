import { useEffect, useState, useCallback, useRef } from 'react'
import { useChat } from '@ai-sdk/react'
import { DefaultChatTransport } from 'ai'
import { useSession } from 'next-auth/react'
import type { Msg } from '@/app/types/ai-sdk5'
import { isTitleDataPart, extractTextContent } from '@/app/types/ai-sdk5'
import { useUIStore } from '@/app/stores/ui_store'
import { useAiPaneStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useAiPaneStore'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { useTabStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore'
import { extractNodeContentText } from '@/app/utils/nodeContent'
import { AIPaneGenerateRequest } from '@/app/api/aipane/generate/types'

export type AiGenerateOptions = {
  prompt: string
  model: string
  contextIds: string[]
  tabId: string
  dragTreeId: string
  canGenerate?: boolean // Prevent generation until conditions are met
}

export const useAiGeneration = ({
  prompt,
  model,
  contextIds,
  tabId,
  dragTreeId,
  canGenerate = true,
}: AiGenerateOptions) => {
  const { data: session } = useSession()
  const useRealLLMAPI = useUIStore(state => state.useRealLLMAPI)
  const apiEndpoint = useRealLLMAPI
    ? '/api/aipane/generate'
    : '/api/aipane/generate-simulator'

  // Get selected context items, node content, and template name
  const settingsContext = useAiPaneStore(state => state.settings.context)
  const promptTemplate = useAiPaneStore(state => state.settings.promptTemplate)
  const nodeContent = useDragTreeStore(state => state.nodeContent)

  // Get generation phase and update function from tab store
  const generationPhase = useTabStore(
    state =>
      state.tabs.find(t => t.id === tabId)?.aiPaneData?.generationPhase ??
      'idle'
  )
  const updateTabAiPaneData = useTabStore(state => state.updateTabAiPaneData)
  const updateTabTitle = useTabStore(state => state.updateTabTitle)
  const tryStartGeneration = useTabStore(state => state.tryStartGeneration)

  // Helper to extract and convert context content
  const getContextMarkdown = () => {
    const selectedItems = settingsContext.filter(item => item.selected)
    console.log(
      '🔍 [Context Debug] Selected items:',
      selectedItems.length,
      selectedItems.map(i => ({ id: i.id, title: i.title }))
    )

    if (selectedItems.length === 0) {
      console.log(
        '🔍 [Context Debug] No items selected, returning empty context'
      )
      return ''
    }

    const contextParts = selectedItems.map(item => {
      const contentMap = nodeContent.get(item.id)
      console.log(
        `🔍 [Context Debug] Content map for ${item.id} (${item.title}):`,
        contentMap ? `${contentMap.size} items` : 'null'
      )

      if (!contentMap || contentMap.size === 0) {
        console.log(
          `⚠️ [Context Debug] No content found for item ${item.id} (${item.title})`
        )
        return { text: '', item }
      }

      // Try all content items, not just the first one
      const contentItems = Array.from(contentMap.values())
      console.log(
        `🔍 [Context Debug] Content items for ${item.id}:`,
        contentItems.length
      )

      let extractedText = ''
      for (let i = 0; i < contentItems.length; i++) {
        const contentItem = contentItems[i]
        console.log(
          `🔍 [Context Debug] Trying content item ${i} for ${item.id}:`,
          {
            hasContentText: !!(contentItem as any)?.contentText,
            hasMetadata: !!(contentItem as any)?.metadata,
            hasMessages: !!(contentItem as any)?.messages,
          }
        )

        const text = extractNodeContentText(contentItem)
        if (text && text.trim().length > 0) {
          extractedText = text
          console.log(
            `✅ [Context Debug] Found text in content item ${i} for ${item.id}: ${text.length} chars`
          )
          break
        }
      }

      if (!extractedText || extractedText.trim().length === 0) {
        console.log(
          `⚠️ [Context Debug] No valid text extracted for item ${item.id} (${item.title})`
        )
        return { text: '', item }
      }

      console.log(
        `🔍 [Context Debug] Final extracted text for ${item.id}:`,
        extractedText.length,
        'chars'
      )

      // Add item title as header for better context organization
      const contextWithHeader = `## ${item.title}\n\n${extractedText}`
      return { text: contextWithHeader, item }
    })

    const validParts = contextParts.filter(
      part => part.text && part.text.trim().length > 0
    )
    const finalContext = validParts.map(part => part.text).join('\n\n---\n\n')

    console.log('🔍 [Context Debug] Final context summary:', {
      selectedCount: selectedItems.length,
      validPartsCount: validParts.length,
      totalLength: finalContext.length,
      failedItems: contextParts
        .filter(part => !part.text || part.text.trim().length === 0)
        .map(part => ({ id: part.item.id, title: part.item.title })),
      preview:
        finalContext.substring(0, 200) +
        (finalContext.length > 200 ? '...' : ''),
    })

    return finalContext
  }

  const chat = useChat<Msg>({
    transport: new DefaultChatTransport({
      api: apiEndpoint,
      // Custom fetch wrapper lets us intercept response headers while still
      // letting the Vercel AI SDK handle streaming. This avoids issuing two
      // separate network requests (previous implementation did a manual fetch
      // and then a useChat request, creating duplicate generations).
      fetch: async (url: RequestInfo | URL, options?: RequestInit) => {
        // Add abort signal to fetch options for cancellation support
        const fetchOptions = {
          ...options,
          signal: abortControllerRef.current?.signal,
        }

        const res = await fetch(url, fetchOptions)
        const serverGenerationId = res.headers.get('X-Generation-Id')
        if (serverGenerationId) {
          generationIdRef.current = serverGenerationId
          setGenerationId(serverGenerationId)
          console.log(
            '📝 [AI Generation Hook] captured generation id from header:',
            serverGenerationId
          )
        }
        return res
      },
    }),
    id: `generate-${tabId}`,
    // Note: throttle not available in this AI SDK version
    onFinish: () => {
      // Set phase to completed (allows manual regeneration via UI)
      updateTabAiPaneData(tabId, {
        generationPhase: 'completed',
        generationStarted: false,
      })

      // Clean up the abort controller on successful completion
      abortControllerRef.current = null

      // Nothing extra to do – generationId is already set from fetch hook
      if (!generationIdRef.current) {
        console.warn(
          '⚠️ [AI Generation Hook] generation id missing after finish'
        )
      }
    },
    onError: error => {
      // AI SDK 5 best practice: Proper error classification
      const isAbortError =
        error.name === 'AbortError' ||
        error.message?.includes('aborted') ||
        error.message?.includes('cancelled')

      const isRateLimitError =
        error.message?.includes('rate limit') || error.message?.includes('429')

      const isNetworkError =
        error.message?.includes('network') || error.message?.includes('fetch')

      if (isAbortError) {
        // Set phase to idle on cancellation (allows retry)
        updateTabAiPaneData(tabId, {
          generationPhase: 'idle',
          generationStarted: false,
        })
        console.log('🛑 [AI Generation Hook] generation cancelled by user')
      } else if (isRateLimitError) {
        // Handle rate limiting with backoff suggestion
        updateTabAiPaneData(tabId, {
          generationPhase: 'failed',
          generationStarted: false,
        })
        console.warn('⚠️ [AI Generation Hook] rate limited:', error)
      } else if (isNetworkError) {
        // Handle network issues with retry suggestion
        updateTabAiPaneData(tabId, {
          generationPhase: 'failed',
          generationStarted: false,
        })
        console.warn('🌐 [AI Generation Hook] network error:', error)
      } else {
        // Set phase to failed for other errors (allows retry via UI)
        updateTabAiPaneData(tabId, {
          generationPhase: 'failed',
          generationStarted: false,
        })
        console.error('🚨 [AI Generation Hook] generation failed:', error)
      }

      // Clean up the abort controller
      abortControllerRef.current = null
    },
  })

  // Extract properties from chat object for AI SDK 5 compatibility
  const { messages } = chat
  const isLoading = chat.status === 'streaming' || chat.status === 'submitted'

  // Create append function for compatibility
  const append = useCallback(
    (
      message: { role: 'user' | 'assistant'; content: string },
      options?: any
    ) => {
      chat.sendMessage({ text: message.content }, options)
    },
    [chat]
  )

  /**
   * Track completion state based on streaming and messages.
   * The main generation control is now handled by generationPhase state machine.
   */
  const [completed, setCompleted] = useState<boolean>(false)

  // State for tracking server-provided generation ID from response headers
  const [generationId, setGenerationId] = useState<string | null>(null)
  const generationIdRef = useRef<string | null>(null)

  // AbortController for cancellation support
  const abortControllerRef = useRef<AbortController | null>(null)

  // State for tracking streamed title data (cleanup - not currently used in UI)
  // const [streamedTitle, setStreamedTitle] = useState<string | null>(null)

  // Process streamed data for real-time title updates using AI SDK 5 data parts
  useEffect(() => {
    if (messages && messages.length > 0) {
      const lastMessage = messages[messages.length - 1]
      if (lastMessage && lastMessage.parts) {
        // Look for title data parts in the latest message
        for (const part of lastMessage.parts) {
          if (isTitleDataPart(part)) {
            const title = part.data.title
            if (title) {
              console.log(
                '📝 [AI Generation Hook] received streamed title:',
                title
              )
              // Update tab title in real-time
              updateTabTitle(tabId, title)
            }
          }
        }
      }
    }
  }, [messages, tabId, updateTabTitle])

  // Start streaming exactly once
  const start = useCallback(() => {
    // If this tab already has a persisted generation asset, do NOT auto-generate.
    const existingAssetId = useTabStore
      .getState()
      .tabs.find(t => t.id === tabId)?.aiPaneData?.assetId
    if (existingAssetId) {
      console.log(
        'ℹ️ [AI Generation Hook] Skipping auto-generate because asset already exists:',
        existingAssetId
      )
      return
    }

    // Prevent generation if content is not loaded
    if (!canGenerate) {
      return
    }

    // Prevent generation if messages already exist (indicates prior generation)
    if (messages.length > 0) {
      console.log(
        '🚫 [AI Generation Hook] Cannot start - messages already exist'
      )
      return
    }

    // Atomically try to start generation - this prevents race conditions
    if (!tryStartGeneration(tabId)) {
      console.log('🚫 [AI Generation Hook] Cannot start - already generating')
      return
    }

    console.log('✅ [AI Generation Hook] Starting generation')

    // Allow starting only for 'idle' and 'failed' phases. Prevent auto-repeat on 'completed'.
    if (!['idle', 'failed'].includes(generationPhase)) {
      console.log(
        '🚫 [AI Generation Hook] Cannot start - invalid phase:',
        generationPhase
      )
      // Reset the flag since we won't proceed
      updateTabAiPaneData(tabId, {
        generationStarted: false,
        generationPhase: 'idle',
      })
      return
    }

    if (!session?.user?.id) {
      // Reset the flag since we won't proceed
      updateTabAiPaneData(tabId, {
        generationStarted: false,
        generationPhase: 'idle',
      })
      return
    }

    // Create new AbortController for this generation
    abortControllerRef.current = new AbortController()
    console.log(
      '🎯 [AI Generation Hook] Created new AbortController for generation'
    )

    const contextMarkdown = getContextMarkdown()
    const finalPrompt =
      (contextMarkdown ? `# Context\n${contextMarkdown}\n\n` : '') +
      `# Instructions\n${prompt}`

    console.log('🚀 [AI Generation] Prompt construction:', {
      hasContext: !!contextMarkdown,
      contextLength: contextMarkdown.length,
      userPromptLength: prompt.length,
      finalPromptLength: finalPrompt.length,
      contextIds: contextIds,
      selectedContextCount: settingsContext.filter(item => item.selected)
        .length,
    })

    console.log(
      '🚀 [AI Generation] Final prompt preview:',
      finalPrompt.substring(0, 200) + '...'
    )

    const body: AIPaneGenerateRequest = {
      prompt: finalPrompt,
      model,
      settings: {
        contextIds, // Persist selected context IDs
        userPrompt: prompt, // Persist original user prompt for settings display
      },
      dragTreeId,
      entityType: 'drag_tree_v1',
      promptTemplate, // Include template name for smart title generation
    }

    // Phase is already set to 'generating' by tryStartGeneration
    // Single call – append triggers fetch with header capture
    append({ role: 'user', content: finalPrompt }, { body } as any)
  }, [
    generationPhase,
    canGenerate,
    append,
    prompt,
    model,
    dragTreeId,
    session?.user?.id,
    promptTemplate,
    getContextMarkdown,
    tabId,
    updateTabAiPaneData,
    tryStartGeneration,
    contextIds,
    messages,
  ])

  // Determine completion when streaming ends and we have an assistant message
  useEffect(() => {
    if (
      generationPhase === 'generating' &&
      !isLoading &&
      messages.some(m => m.role === 'assistant' && extractTextContent(m))
    ) {
      setCompleted(true)
    }
  }, [generationPhase, isLoading, messages])

  // State recovery: Check if there's an ongoing or completed generation on mount
  useEffect(() => {
    const recoverState = async () => {
      const assetId = useTabStore.getState().tabs.find(t => t.id === tabId)
        ?.aiPaneData?.assetId

      // Only attempt recovery if we think we're generating but have no generationId
      if (generationPhase === 'generating' && !generationId && assetId) {
        console.log(
          '🔄 [AI Generation Hook] Attempting state recovery for assetId:',
          assetId
        )

        try {
          // Check the database to see the actual status
          const { getAIGenerationContent } = await import(
            '@/app/server-actions/drag-tree/get_ai_generation_content'
          )
          const dbGeneration = await getAIGenerationContent(assetId)

          if (dbGeneration) {
            console.log(
              '🔄 [AI Generation Hook] Found DB generation with status:',
              dbGeneration.status
            )

            // Sync frontend state with database state
            if (dbGeneration.status === 'ACTIVE') {
              // Generation completed while we were away
              updateTabAiPaneData(tabId, { generationPhase: 'completed' })
              setGenerationId(dbGeneration.id)
              generationIdRef.current = dbGeneration.id
              console.log(
                '✅ [AI Generation Hook] Recovered completed generation'
              )
            } else if (dbGeneration.status === 'GENERATING') {
              // Still generating - this might be stale, reset to idle to allow restart
              updateTabAiPaneData(tabId, { generationPhase: 'idle' })
              console.log(
                '🔄 [AI Generation Hook] Reset stale generating state to idle'
              )
            } else {
              // Failed or inactive - set to failed to allow retry
              updateTabAiPaneData(tabId, { generationPhase: 'failed' })
              console.log(
                '❌ [AI Generation Hook] Recovered failed generation state'
              )
            }
          } else {
            // No generation found in DB - reset to idle
            updateTabAiPaneData(tabId, { generationPhase: 'idle' })
            console.log(
              '🔄 [AI Generation Hook] No DB generation found, reset to idle'
            )
          }
        } catch (error) {
          console.error('🚨 [AI Generation Hook] State recovery failed:', error)
          // On recovery failure, reset to idle to allow fresh start
          updateTabAiPaneData(tabId, { generationPhase: 'idle' })
        }
      }
    }

    recoverState()
  }, []) // Only run on mount

  // Kick-off streaming on initial mount (or if dependencies that affect prompt change).
  useEffect(() => {
    start()
  }, [start])

  // Latest assistant message – used by UI
  const markdown =
    extractTextContent(
      messages.filter(m => m.role === 'assistant').at(-1) || ({} as Msg)
    ) || ''

  // Manual retry function for UI
  const retryGeneration = useCallback(() => {
    updateTabAiPaneData(tabId, { generationPhase: 'idle' })
    // Hook will automatically restart on next render due to useEffect dependency
  }, [tabId, updateTabAiPaneData])

  // Cancel ongoing generation
  const cancelGeneration = useCallback(() => {
    if (abortControllerRef.current && generationPhase === 'generating') {
      console.log('🛑 [AI Generation Hook] User cancelled generation')
      abortControllerRef.current.abort()
      // Note: onError callback will handle setting phase to 'idle' and cleanup
    }
  }, [generationPhase])

  // Cleanup AbortController on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
        abortControllerRef.current = null
      }
    }
  }, [])

  return {
    markdown,
    isStreaming: isLoading,
    isCompleted: completed,
    generationId: generationId, // Now comes from server headers
    generationPhase, // Expose phase for UI state management
    retryGeneration, // Allow manual retry
    cancelGeneration, // Allow manual cancellation
    // streamedTitle, // removed - not used in UI
  }
}
