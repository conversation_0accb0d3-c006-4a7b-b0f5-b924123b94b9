import { create } from 'zustand'
import { getAssetType, isValidAssetType } from '@/lib/asset-types'
import { useTabStore, type Tab } from './useTabStore'
import type { Asset } from './useAssetStore'

/**
 * Asset Tab Manager Interface
 * Handles asset-specific tab operations using the asset type registry
 */
export interface AssetTabManager {
  /**
   * Add a new asset tab using registry-driven logic
   */
  addAssetTab: (asset: Asset) => string

  /**
   * Update asset-specific tab data
   */
  updateAssetTabData: (
    tabId: string,
    updates: Partial<Tab['aiPaneData']>
  ) => void

  /**
   * Get the tab component for an asset type
   */
  getAssetTabComponent: (
    type: string
  ) => React.ComponentType<{ tab: Tab; dragTreeId: string }> | null

  /**
   * Check if a tab is an asset tab
   */
  isAssetTab: (tab: Tab) => boolean

  /**
   * Get asset type definition for a tab
   */
  getAssetTypeForTab: (tab: Tab) => ReturnType<typeof getAssetType>

  /**
   * Create asset tab title using registry
   */
  createAssetTabTitle: (asset: Asset) => string

  /**
   * Create asset tab ID using registry
   */
  createAssetTabId: (asset: Asset) => string
}

/**
 * Asset Tab Manager Store
 * Provides registry-driven asset tab management
 */
export const useAssetTabManager = create<AssetTabManager>((set, get) => ({
  addAssetTab: (asset: Asset) => {
    const assetType = getAssetType(asset.type)
    if (!assetType) {
      console.error(`Unknown asset type: ${asset.type}`)
      throw new Error(`Unknown asset type: ${asset.type}`)
    }

    const tabId = get().createAssetTabId(asset)

    // Check if a tab for this asset already exists
    const tabStore = useTabStore.getState()
    const existingTab = tabStore.tabs.find(
      tab => tab.id === tabId || tab.aiPaneData?.assetId === asset.id
    )

    if (existingTab) {
      // Simply switch focus to the existing tab
      tabStore.setActiveTab(existingTab.id)
      return existingTab.id
    }

    // Create new tab using registry-driven logic
    const newTab: Tab = {
      id: tabId,
      title: get().createAssetTabTitle(asset),
      fullTitle: get().createAssetTabTitle(asset),
      type: asset.type, // Now properly extensible
      isClosable: true,
      aiPaneData: {
        ...assetType.createAssetTabData?.(asset),
        // Ensure core fields are always present
        type: asset.type,
        model: asset.model,
        prompt: asset.prompt,
        contextIds: asset.contextIds,
        settings: {},
        assetId: asset.id,
      },
    }

    // Add tab using core tab store
    tabStore.tabs.push(newTab)
    tabStore.setActiveTab(newTab.id)

    console.log(`✅ Created asset tab: ${asset.type} - ${asset.title}`)
    return newTab.id
  },

  updateAssetTabData: (tabId: string, updates: Partial<Tab['aiPaneData']>) => {
    const tabStore = useTabStore.getState()
    tabStore.updateTabAiPaneData(tabId, updates)
  },

  getAssetTabComponent: (type: string) => {
    const assetType = getAssetType(type)
    return assetType?.tabComponent || null
  },

  isAssetTab: (tab: Tab) => {
    return tab.aiPaneData !== undefined && isValidAssetType(tab.type)
  },

  getAssetTypeForTab: (tab: Tab) => {
    return getAssetType(tab.type)
  },

  createAssetTabTitle: (asset: Asset) => {
    const assetType = getAssetType(asset.type)
    if (assetType?.createTabTitle) {
      return assetType.createTabTitle(asset)
    }
    // Fallback to legacy logic
    return `${asset.type === 'generate' ? 'Generate' : 'Chat'} - ${asset.title}`
  },

  createAssetTabId: (asset: Asset) => {
    return `asset-${asset.type}-${asset.id}`
  },
}))

/**
 * Hook to get asset tab manager functions
 */
export const useAssetTabActions = () => {
  const manager = useAssetTabManager()
  return {
    addAssetTab: manager.addAssetTab,
    updateAssetTabData: manager.updateAssetTabData,
    isAssetTab: manager.isAssetTab,
    getAssetTypeForTab: manager.getAssetTypeForTab,
  }
}

/**
 * Hook to get asset tab component resolver
 */
export const useAssetTabComponentResolver = () => {
  const getAssetTabComponent = useAssetTabManager(
    state => state.getAssetTabComponent
  )
  return getAssetTabComponent
}
