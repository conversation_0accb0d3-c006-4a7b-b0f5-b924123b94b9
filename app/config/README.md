# LLM Model Configuration System

This directory contains the centralized configuration system for LLM models based on user subscription tiers.

## Overview

The LLM model configuration system provides:

- **Centralized model management**: All model configurations are defined in one place
- **Subscription-based model assignment**: Different models for different subscription tiers
- **Type safety**: Full TypeScript support with compile-time validation
- **Easy extensibility**: Simple to add new tiers, routes, or model providers
- **Caching**: User subscription tier caching to reduce database queries

## Architecture

### Core Files

- `app/config/llm-models.ts` - Model configuration definitions and types
- `app/libs/model-config.ts` - Utility functions for retrieving model configs

### Configuration Structure

```typescript
type ModelConfig = {
  model: string // Model name (e.g., 'gpt-4.1')
  model_provider: string // Provider ('azure', 'openai', etc.)
  temperature?: number // Optional temperature setting
  maxTokens?: number // Optional max tokens setting
}

type SubscriptionModelConfig = {
  [SubscriptionTier]: {
    [APIRoute]: ModelConfig
  }
}
```

## Current Configuration

### Subscription Tiers

- **FREE**: Cost-effective models for basic functionality
  - Uses `gpt-4.1-mini` for all routes
  - Lower token limits (2000)

- **GUEST**: Premium models for enhanced experience
  - Uses `gpt-4.1` for all routes
  - Higher token limits (4000)

### Supported API Routes

- `dragtree_generate_questions` - DragTree question generation
- `dragtree_research_generate` - DragTree research with web search
- `dragtree_generate_similar_questions` - Generate similar questions for DragTree nodes
- `dragtree_generate_similar_categories` - Generate similar categories for DragTree nodes
- `screening_diagnose` - Problem analysis and screening
- `screening_rephrase` - Problem statement rephrasing
- `aipane_generate` - AI Pane content generation
- `aipane_chat` - AI Pane chat conversations

## Usage

### In API Routes

```typescript
import { getModelConfig } from '@/app/libs/model-config'

export async function POST(request: NextRequest) {
  // Get user authentication
  const authResult = await validateAuthentication()
  if (!authResult.success) return authResult.error

  // Get model configuration for this user and route
  const modelConfig = await getModelConfig(
    authResult.userId,
    'dragtree_generate_questions'
  )

  // Use the configuration
  const result = await streamText({
    model: azure(modelConfig.model),
    temperature: modelConfig.temperature,
    maxTokens: modelConfig.maxTokens,
    // ... other options
  })
}
```

### Utility Functions

```typescript
// Get model config (async - fetches user tier from DB)
const config = await getModelConfig(userId, 'screening_diagnose')

// Get model config if you already know the tier (sync)
const config = getModelConfigSync(SubscriptionTier.GUEST, 'screening_diagnose')

// Cache management
clearUserTierCache(userId) // Clear specific user
clearUserTierCache() // Clear all cache

// Monitoring
const stats = getTierCacheStats()
console.log(
  `Cache size: ${stats.size}, oldest entry: ${stats.oldestEntryAge}ms`
)
```

## Adding New Features

### Adding a New API Route

1. Add the route to the `APIRoute` type in `llm-models.ts`:

```typescript
export type APIRoute =
  | 'dragtree_generate_questions'
  | 'dragtree_research_generate'
  | 'screening_diagnose'
  | 'screening_rephrase'
  | 'aipane_generate'
  | 'aipane_chat'
  | 'new_route_name' // Add here
```

2. Add configurations for all subscription tiers:

```typescript
export const LLM_MODEL_CONFIG: SubscriptionModelConfig = {
  FREE: {
    // ... existing configs
    new_route_name: {
      model: 'gpt-4.1-mini',
      model_provider: 'azure',
      temperature: 0.7,
      maxTokens: 2000,
    },
  },
  GUEST: {
    // ... existing configs
    new_route_name: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxTokens: 4000,
    },
  },
}
```

3. Update the validation in `validateModelConfig()` function

### Adding a New Subscription Tier

1. Add the tier to the Prisma schema enum
2. Add configuration in `LLM_MODEL_CONFIG`
3. Update the validation function

### Adding a New Model Provider

1. Add to the `ModelProvider` type
2. Update API routes to handle the new provider
3. Add provider-specific configuration options to `ModelConfig`

## Caching Strategy

- User subscription tiers are cached for 5 minutes
- Cache is automatically cleared when users are updated
- Manual cache clearing available for immediate updates
- Cache statistics available for monitoring

## Error Handling

- Falls back to `DEFAULT_MODEL_CONFIG` if user tier not found
- Falls back to FREE tier if database query fails
- Comprehensive logging for debugging
- Graceful degradation ensures API availability

## Migration Notes

This system replaces hardcoded model assignments in API routes. The migration:

1. ✅ Maintains backward compatibility
2. ✅ Preserves existing functionality
3. ✅ Adds subscription-based differentiation
4. ✅ Improves maintainability and type safety

## Complete API Coverage

**All LLM-using APIs are now centrally managed (8 total routes):**

1. `dragtree_generate_questions` ✅
2. `dragtree_research_generate` ✅
3. `dragtree_generate_similar_questions` ✅
4. `dragtree_generate_similar_categories` ✅
5. `screening_diagnose` ✅
6. `screening_rephrase` ✅
7. `aipane_generate` ✅
8. `aipane_chat` ✅

## Build-Time Validation

The system includes comprehensive tests that run during build time to ensure:

- All subscription tiers have complete configurations
- All API routes are configured for each tier
- Model configurations have required properties
- No missing or invalid configurations

Tests are located in `__tests__/config/llm-model-config.test.ts` and will cause the build to fail if any configuration issues are detected.

## Future Enhancements

- Support for multiple model providers per route
- Dynamic model selection based on load/availability
- A/B testing framework for model performance
- Usage analytics and cost optimization
- Admin interface for configuration management
