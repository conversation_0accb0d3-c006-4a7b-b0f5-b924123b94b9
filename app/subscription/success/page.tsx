'use client'

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import Confetti from 'react-confetti'

function SuccessPage() {
  const router = useRouter()
  const { update } = useSession()
  const [windowSize, setWindowSize] = useState({ width: 0, height: 0 })

  const handleGoBack = () => {
    router.push('/conversations')
  }

  useEffect(() => {
    // Refresh JWT/session so upgraded subscription tier is available immediately
    update?.()
    const updateWindowSize = () => {
      setWindowSize({ width: window.innerWidth, height: window.innerHeight })
    }

    updateWindowSize()
    window.addEventListener('resize', updateWindowSize)
    return () => window.removeEventListener('resize', updateWindowSize)
  }, [])

  return (
    <div className="text-center flex flex-col items-center justify-center h-screen relative bg-gradient-to-b from-purple-400 to-indigo-600">
      <Confetti
        width={windowSize.width}
        height={windowSize.height}
        style={{ position: 'fixed', top: 0, left: 0, zIndex: 0 }}
      />
      <div className="z-10 flex flex-col items-center p-8 bg-white bg-opacity-20 rounded-xl backdrop-blur-md">
        <h1 className="text-5xl font-extrabold text-white mb-6 animate-pulse">
          Subscription Successful!
        </h1>
        <p className="text-2xl text-yellow-300 mb-8 font-semibold tracking-wide">
          Thank you for subscribing!
        </p>
        <Button
          onClick={handleGoBack}
          className="mt-4 bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-6 rounded-full transition duration-300 ease-in-out transform hover:scale-105"
        >
          Go back
        </Button>
      </div>
    </div>
  )
}

export default SuccessPage
