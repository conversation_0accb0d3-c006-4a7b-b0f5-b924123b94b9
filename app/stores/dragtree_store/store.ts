/**
 * DragTree Store
 *
 * Central state management for the drag tree functionality.
 *
 * Architecture:
 * - Zustand store for global state management
 * - Modular utilities for specific operations (tree, node, database)
 * - Automatic database synchronization with debouncing
 * - Content management for AI-generated research
 *
 * Key Design Decisions:
 * - Separation of frontend tree structure (hierarchical) from database structure (flat)
 * - Node map for O(1) lookups while maintaining tree hierarchy
 * - Debounced database operations to prevent excessive API calls
 * - Content versioning support for future A/B testing
 *
 * State Structure:
 * - frontendTreeStructure: Hierarchical tree representation for UI
 * - nodeMap: Flat map for fast node lookups
 * - nodeContent: Multi-level map for research content by node and content ID
 * - pendingDatabaseOperations: Prevents race conditions during sync
 * - contentFetchInProgress: Set to track ongoing fetches to avoid duplicate requests
 */

import { create } from 'zustand'
import { TreeNode } from '@/app/types'
import toast from 'react-hot-toast'
import { parseQuestionsFromMarkdown } from '@/app/(conv)/dragTree/[dragTreeId]/utils/treeHelpers'
import markdownToTreeNode from '@/app/(conv)/dragTree/[dragTreeId]/utils/markdownToTreeNode'
import {
  updateDragTreeNode,
  createDragTreeNode,
  getDragTree,
  updateDragTree,
} from '@/app/server-actions/drag-tree'
import { DragTreeNodeStatus } from '@prisma/client'
import {
  TreeNodeType,
  NodeContentType,
} from '@/app/(conv)/dragTree/[dragTreeId]/constants/node-types'
import debounce from 'lodash/debounce'

// Import utility functions
import {
  DatabaseDragTree,
  reconstructTreeFromDatabase,
  convertTreeToDbStructure,
} from './utils/database-utils'
import {
  calculateNodeLevel,
  buildNodeMap,
  collectInterestedNodes,
  resetAllInterestInTree,
  addNodeToTree,
  deleteNodeFromTree,
  editNodeInTree,
  reorderNodeInTree,
  toggleNodeInterest,
  addMultipleNodesToTree,
  validateTreeConstraints,
  collectAllNodeIds,
  getFormattedPath as getFormattedNodePathFromTree,
} from './utils/tree-utils'
import {
  createNodeMetadata,
  createNewTreeNode,
  createNodesFromText,
} from './utils/node-utils'
import { debouncedDbSync } from './utils/sync-utils'
import {
  validateTreeStructure,
  sanitizeTreeStructure,
} from './utils/tree-validation'
import { performanceMonitor } from '@/app/utils/performance-monitor'

import { DragTreeNodeContentStatus } from '@prisma/client'

/**
 * Node Content Item Type
 *
 * Represents AI-generated content associated with tree nodes.
 * Supports multiple content types and versioning for future extensibility.
 *
 * Design Notes:
 * - contentId: Unique identifier for this content instance
 * - contentType: Allows different types of AI-generated content
 * - contentVersion: Enables A/B testing and content evolution
 * - status: Tracks content lifecycle (INITIALIZED -> PROCESSING -> ACTIVE)
 * - metadata: Stores search results, citations, and other structured data
 * - messages: Full conversation history for research continuation
 */
export type NodeContentItem = {
  contentId: string
  contentType: NodeContentType
  contentVersion: string
  status: DragTreeNodeContentStatus
  contentText: string
  metadata: Record<string, any>
  messages?: any[] // For storing conversation history
}

/**
 * Tree Store Type Definition
 *
 * Complete type definition for the dragTree store.
 * Organized into logical sections for better maintainability.
 */
export type TreeStore = {
  // Core State
  frontendTreeStructure: TreeNode | null // Hierarchical tree for UI rendering
  screeningQuestion: string // Context question for research prompts
  dragTreeId: string | null // Database identifier for persistence
  userId: string | null // User identifier for data ownership
  dragTreeTitle: string | null // Human-readable tree title
  preferredLanguage: string | null // Language preference for AI interactions

  // Performance Optimizations
  nodeMap: Map<string, TreeNode> // Flat map for O(1) node access
  pendingDatabaseOperations: Set<string> // Prevents race conditions during sync

  // Content Management - Nested structure: nodeId -> contentId -> ContentItem
  // This allows multiple AI-generated content pieces per node (future versioning)
  nodeContent: Map<string, Map<string, NodeContentItem>>
  researchedNodeIds: Set<string> // New: Track nodes with completed research

  // Track ongoing fetches to avoid duplicate requests
  contentFetchInProgress: Set<string>

  // Tree Structure Methods
  setFrontendTreeStructure: (
    data: TreeNode | DatabaseDragTree | null,
    skipSync?: boolean
  ) => void
  setScreeningQuestion: (question: string) => void

  // Node CRUD Operations (with auto-sync to database)
  addNode: (parentId: string, type: TreeNodeType) => void
  deleteNode: (nodeId: string, nodeType: string) => void
  editNode: (nodeId: string, newLabel: string) => void
  reorderNode: (parentId: string, oldIndex: number, newIndex: number) => void

  // AI-Generated Content Addition
  addSimilarQuestions: (parentId: string, questionsMarkdown: string) => void
  addSimilarCategories: (parentId: string, categoriesMarkdown: string) => void

  // Node Utilities
  findNodeById: (nodeId: string) => TreeNode | null
  getNodePath: (nodeId: string) => string
  markNodeAsInterested: (nodeId: string) => void
  getInterestedNodes: () => TreeNode[]
  resetAllInterest: () => void

  // Database Synchronization
  setDragTreeId: (dragTreeId: string, userId: string) => void
  setDragTreeTitle: (title: string | null) => void
  setPreferredLanguage: (language: string | null) => void
  resetDragTreeData: () => void
  loadFromDatabase: () => Promise<void>

  // Internal Utilities
  rebuildNodeMap: () => void // Rebuilds nodeMap from tree structure
  debouncedRebuildNodeMap: () => void // Debounced version to group rapid rebuilds

  // Research Content Management
  addNodeContent: (nodeId: string, contentItem: NodeContentItem) => void
  updateNodeContent: (
    nodeId: string,
    contentId: string,
    updates: Partial<NodeContentItem>
  ) => void
  getNodeContent: (nodeId: string) => Map<string, NodeContentItem> | undefined
  getSpecificContent: (
    nodeId: string,
    contentId: string
  ) => NodeContentItem | undefined
  markNodeAsResearched: (nodeId: string) => void // New: Action to mark node as researched
  // Lazy loading
  fetchNodeContent: (nodeId: string, contentId: string) => Promise<void>
  fetchNodeContentBatch: (
    pairs: Array<{ nodeId: string; contentId: string }>
  ) => Promise<void>

  isContentFetching: (contentId: string) => boolean
}

export const useDragTreeStore = create<TreeStore>((set, get) => ({
  // State
  frontendTreeStructure: null,
  screeningQuestion: '',
  dragTreeId: null,
  userId: null,
  dragTreeTitle: null,
  preferredLanguage: null,
  nodeMap: new Map(),
  pendingDatabaseOperations: new Set(),
  nodeContent: new Map(),
  contentFetchInProgress: new Set(),
  researchedNodeIds: new Set(), // New: Initialize as empty set

  setFrontendTreeStructure: (data, skipSync = false) => {
    const endTiming = performanceMonitor.startTiming('setFrontendTreeStructure')

    console.log(
      '🏗️ [DragTreeStore] setFrontendTreeStructure called with:',
      data
    )

    if (!data) {
      console.log('🏗️ [DragTreeStore] Clearing tree structure')
      set({ frontendTreeStructure: null, nodeContent: new Map() })
      get().rebuildNodeMap()
      endTiming()
      return
    }

    // Check if data is a DatabaseDragTree (has nodes array)
    if (data && typeof data === 'object' && 'nodes' in data) {
      console.log('🏗️ [DragTreeStore] Converting DatabaseDragTree to TreeNode')
      const dbData = data as DatabaseDragTree
      const treeNode = reconstructTreeFromDatabase(dbData)

      if (treeNode) {
        // Validate the converted tree structure
        const validation = validateTreeStructure(treeNode)

        if (!validation.isValid) {
          console.warn(
            '⚠️ [DragTreeStore] Tree validation failed, attempting sanitization:',
            validation.errors
          )

          const sanitizedTree = sanitizeTreeStructure(treeNode)
          if (sanitizedTree) {
            const sanitizedValidation = validateTreeStructure(sanitizedTree)
            if (sanitizedValidation.isValid) {
              console.log('✅ [DragTreeStore] Tree sanitized successfully')
              set({ frontendTreeStructure: sanitizedTree })
              get().rebuildNodeMap()
              endTiming({ nodeCount: sanitizedValidation.stats.totalNodes })
            } else {
              console.error(
                '❌ [DragTreeStore] Tree sanitization failed, using original'
              )
              set({ frontendTreeStructure: treeNode })
              get().rebuildNodeMap()
              endTiming({
                nodeCount: validation.stats.totalNodes,
                errorCount: 1,
              })
            }
          } else {
            console.error(
              '❌ [DragTreeStore] Tree sanitization returned null, using original'
            )
            set({ frontendTreeStructure: treeNode })
            get().rebuildNodeMap()
            endTiming({ nodeCount: validation.stats.totalNodes, errorCount: 1 })
          }
        } else {
          console.log(
            '✅ [DragTreeStore] Successfully converted and validated TreeNode:',
            validation.stats
          )
          set({ frontendTreeStructure: treeNode })
          get().rebuildNodeMap()
          endTiming({ nodeCount: validation.stats.totalNodes })
        }

        // Load content items from database data
        if (dbData.nodes) {
          console.log(
            '📝 [DragTreeStore] Loading nodeContent from DatabaseDragTree'
          )
          const newNodeContent = new Map<string, Map<string, NodeContentItem>>()

          dbData.nodes.forEach((node: any) => {
            if (node.content_items && node.content_items.length > 0) {
              const nodeContentMap = new Map<string, NodeContentItem>()

              node.content_items.forEach((contentItem: any) => {
                // Only load ACTIVE content items
                if (contentItem.status !== 'INACTIVE') {
                  const nodeContentItem: NodeContentItem = {
                    contentId: contentItem.id,
                    contentType:
                      contentItem.content_type ||
                      NodeContentType.QUICK_RESEARCH,
                    contentVersion: contentItem.content_version || 'v1',
                    status: contentItem.status,
                    contentText: contentItem.content_text || '',
                    metadata: contentItem.content_metadata || {}, // This includes search results and favicons
                    messages: contentItem.messages || [],
                  }
                  nodeContentMap.set(contentItem.id, nodeContentItem)
                }
              })

              if (nodeContentMap.size > 0) {
                newNodeContent.set(node.id, nodeContentMap)
              }
            }
          })

          set({ nodeContent: newNodeContent })
          console.log(
            `📝 [DragTreeStore] Loaded nodeContent for ${newNodeContent.size} nodes from DatabaseDragTree`
          )
        }
      } else {
        console.warn('❌ [DragTreeStore] Failed to convert DatabaseDragTree')
        set({ frontendTreeStructure: null, nodeContent: new Map() })
        get().rebuildNodeMap()
      }
    } else {
      // Direct TreeNode assignment
      console.log('🏗️ [DragTreeStore] Direct TreeNode assignment')
      set({ frontendTreeStructure: data as TreeNode })
      get().rebuildNodeMap()
    }

    // Note: rebuildNodeMap() already called in each branch above
    // Sync changes with the database unless explicitly skipped (initial load)
    if (!skipSync) {
      debouncedDbSync(async () => {
        const { dragTreeId, frontendTreeStructure } = get()
        if (dragTreeId && frontendTreeStructure) {
          await updateDragTree({
            treeId: dragTreeId,
            treeStructure: convertTreeToDbStructure(frontendTreeStructure),
          })
        }
      })
    }
  },

  setScreeningQuestion: question => set({ screeningQuestion: question }),

  setDragTreeId: (dragTreeId: string, userId: string) => {
    console.log(
      '🔗 [DragTreeStore] setDragTreeId called with:',
      dragTreeId,
      userId
    )

    // If switching to a different drag tree, reset all data first
    const currentDragTreeId = get().dragTreeId
    if (currentDragTreeId && currentDragTreeId !== dragTreeId) {
      console.log(
        '🔄 [DragTreeStore] Switching drag trees, resetting data first'
      )
      get().resetDragTreeData()
    }

    set({ dragTreeId, userId })
  },

  setDragTreeTitle: (title: string | null) => {
    console.log('🏷️ [DragTreeStore] setDragTreeTitle called with:', title)
    set({ dragTreeTitle: title })
  },

  setPreferredLanguage: (language: string | null) => {
    console.log(
      '🌐 [DragTreeStore] setPreferredLanguage called with:',
      language
    )
    set({ preferredLanguage: language })
  },

  resetDragTreeData: () => {
    console.log('🧹 [DragTreeStore] Resetting drag tree data')
    set({
      frontendTreeStructure: null,
      screeningQuestion: '', // Also reset screening question to prevent mixed state
      dragTreeTitle: null,
      preferredLanguage: null,
      nodeMap: new Map(),
      pendingDatabaseOperations: new Set(),
      nodeContent: new Map(),
      contentFetchInProgress: new Set(),
      researchedNodeIds: new Set(), // Reset researchedNodeIds
    })
  },

  loadFromDatabase: async () => {
    const { dragTreeId, userId } = get()

    if (!dragTreeId || !userId) {
      console.warn(
        '🚫 [DragTreeStore] Cannot load: missing dragTreeId or userId'
      )
      return
    }

    console.log('📡 [DragTreeStore] Loading from database...', dragTreeId)

    try {
      const result = await getDragTree(dragTreeId)

      if (result.success && result.data) {
        console.log(
          '✅ [DragTreeStore] Successfully loaded from database:',
          result.data
        )
        // This is a fresh pull from the DB – no need to immediately write it back
        get().setFrontendTreeStructure(result.data, true)
        // Always set the title (null if not available)
        get().setDragTreeTitle(result.data.title || null)
        // Set the preferred language (null if not available)
        get().setPreferredLanguage(result.data.preferred_language || null)
        // Set the screening question (empty string if not available)
        get().setScreeningQuestion(result.data.user_prompt || '')

        // Load nodeContent from database
        if (result.data.nodes) {
          console.log('📝 [DragTreeStore] Loading nodeContent from database')
          const newNodeContent = new Map<string, Map<string, NodeContentItem>>()

          result.data.nodes.forEach((node: any) => {
            if (node.content_items && node.content_items.length > 0) {
              const nodeContentMap = new Map<string, NodeContentItem>()

              node.content_items.forEach((contentItem: any) => {
                // Only load ACTIVE content items
                if (contentItem.status !== 'INACTIVE') {
                  const nodeContentItem: NodeContentItem = {
                    contentId: contentItem.id,
                    contentType:
                      contentItem.content_type ||
                      NodeContentType.QUICK_RESEARCH,
                    contentVersion: contentItem.content_version || 'v1',
                    status: contentItem.status,
                    contentText: contentItem.content_text || '',
                    metadata: contentItem.content_metadata || {},
                    messages: contentItem.messages || [],
                  }
                  nodeContentMap.set(contentItem.id, nodeContentItem)
                }
              })

              if (nodeContentMap.size > 0) {
                newNodeContent.set(node.id, nodeContentMap)
              }
            }
          })

          set({ nodeContent: newNodeContent })
          console.log(
            `📝 [DragTreeStore] Loaded nodeContent for ${newNodeContent.size} nodes`
          )
        }
      } else {
        console.error(
          '❌ [DragTreeStore] Failed to load from database:',
          result.error
        )
      }
    } catch (error) {
      console.error('💥 [DragTreeStore] Database load error:', error)
    }
  },

  addNode: (parentId, type) => {
    console.log(
      `🌱 [DragTreeStore] addNode called: parentId=${parentId}, type=${type}`
    )
    const { frontendTreeStructure, dragTreeId, userId } = get()
    if (!frontendTreeStructure || !dragTreeId) {
      console.warn('❌ [DragTreeStore] Missing tree structure or dragTreeId')
      return
    }

    // Find parent node and calculate level
    const parentNode = get().findNodeById(parentId)
    if (!parentNode) {
      console.warn(`❌ [DragTreeStore] Parent node ${parentId} not found`)
      return
    }

    const parentLevel = calculateNodeLevel(frontendTreeStructure, parentId)
    const newNodeLevel = parentLevel + 1

    // Create new node using utility function
    const newNode = createNewTreeNode(dragTreeId, type)
    const nodeMetadata = createNodeMetadata(parentId, newNodeLevel)

    console.log(`🏗️ [DragTreeStore] Creating new node:`, {
      id: newNode.id,
      label: newNode.label,
      type,
      parentId,
      level: newNodeLevel,
      metadata: nodeMetadata,
    })

    // Add node to tree using utility function
    const newTree = addNodeToTree(frontendTreeStructure, parentId, newNode)
    if (!newTree) {
      console.warn('❌ [DragTreeStore] Failed to add node to tree')
      return
    }

    set({ frontendTreeStructure: newTree })
    set(state => {
      const newMap = new Map(state.nodeMap)
      newMap.set(newNode.id, newNode)
      // If a CATEGORY adds an auto child QUESTION include it as well
      if (type === TreeNodeType.CATEGORY && newNode.children.length > 0) {
        const child = newNode.children[0]
        newMap.set(child.id, child)
      }
      return { nodeMap: newMap }
    })
    toast.success(
      `Added new ${type}${
        type === TreeNodeType.CATEGORY ? ' with question' : ''
      }`
    )

    // Database sync - IMMEDIATE atomic operations for node addition
    if (dragTreeId && userId) {
      console.log(
        `💾 [DragTreeStore] Syncing new ${type} node: ${newNode.id} (IMMEDIATE)`
      )

      // Track this operation to prevent overwrites
      const operationKey = `addNode:${newNode.id}`
      const currentPending = get().pendingDatabaseOperations
      currentPending.add(operationKey)
      set({ pendingDatabaseOperations: new Set(currentPending) })

      // Immediate async operation (no debouncing for critical operations)
      ;(async () => {
        try {
          // Prepare database operations
          const dbOperations: Promise<any>[] = [
            // Create the new node in database with metadata
            createDragTreeNode({
              dragTreeId,
              nodeType:
                newNode.type === TreeNodeType.CATEGORY
                  ? 'CATEGORY'
                  : 'QUESTION',
              label: newNode.label,
              nodeId: newNode.id,
              metadata: nodeMetadata,
            }),
          ]

          // If category, also create the child question
          if (type === TreeNodeType.CATEGORY && newNode.children.length > 0) {
            const childQuestion = newNode.children[0]
            const childQuestionMetadata = createNodeMetadata(
              newNode.id,
              newNodeLevel + 1
            )

            dbOperations.push(
              createDragTreeNode({
                dragTreeId,
                nodeType: 'QUESTION',
                label: childQuestion.label,
                nodeId: childQuestion.id,
                metadata: childQuestionMetadata,
              })
            )
          }

          // Add tree structure update
          dbOperations.push(
            updateDragTree({
              treeId: dragTreeId,
              treeStructure: convertTreeToDbStructure(newTree),
            })
          )

          // Execute all operations atomically
          const results = await Promise.all(dbOperations)
          const nodeResult = results[0]
          const treeResult = results[results.length - 1]

          if (nodeResult.success && treeResult.success) {
            console.log(
              `✅ [DragTreeStore] Successfully synced new ${type} node: ${newNode.id}`
            )
          } else {
            console.error(
              `❌ [DragTreeStore] Failed to sync new node:`,
              nodeResult.error || treeResult.error
            )
            toast.error('Failed to save new node')
          }
        } catch (error) {
          console.error(`❌ [DragTreeStore] Sync error:`, error)
          toast.error('Failed to save new node')
        } finally {
          // Remove the pending operation tracking
          const finalPending = get().pendingDatabaseOperations
          finalPending.delete(operationKey)
          set({ pendingDatabaseOperations: new Set(finalPending) })
          console.log(
            `🧹 [DragTreeStore] Removed pending operation: ${operationKey}`
          )
        }
      })()
    }
  },

  deleteNode: (nodeId, nodeType) => {
    console.log('🗑️ [DragTreeStore] deleteNode called with:', nodeId, nodeType)
    const { frontendTreeStructure, dragTreeId, nodeMap, userId } = get()

    if (!frontendTreeStructure || !dragTreeId) {
      console.warn(
        '❌ [DragTreeStore] Early return - missing frontendTreeStructure or dragTreeId'
      )
      return
    }

    // Validate constraints before deletion
    const validation = validateTreeConstraints(
      frontendTreeStructure,
      nodeId,
      'delete'
    )
    if (!validation.valid) {
      console.warn('❌ [DragTreeStore] Validation failed:', validation.message)
      toast.error(validation.message || 'Cannot delete node')
      return
    }

    // Find the node to collect all affected node IDs
    const nodeToDelete = nodeMap.get(nodeId)
    if (!nodeToDelete) {
      console.warn(`Node ${nodeId} not found`)
      toast.error('Node not found')
      return
    }

    const nodesToMarkInactive = collectAllNodeIds(nodeToDelete)

    // Delete node using utility function
    const newTree = deleteNodeFromTree(frontendTreeStructure, nodeId)
    if (!newTree) {
      console.warn('❌ [DragTreeStore] Failed to delete node from tree')
      toast.error('Failed to delete node')
      return
    }

    console.log(
      '✅ [DragTreeStore] Successfully deleted node from tree, updating state'
    )
    set({ frontendTreeStructure: newTree })
    set(state => {
      const newMap = new Map(state.nodeMap)
      nodesToMarkInactive.forEach(id => newMap.delete(id))
      return { nodeMap: newMap }
    })

    const deletedCount = nodesToMarkInactive.length
    toast.success(
      `Deleted ${nodeType}${
        deletedCount > 1
          ? ` and ${deletedCount - 1} child node${deletedCount > 2 ? 's' : ''}`
          : ''
      }`
    )

    // Database sync with graceful error handling
    if (dragTreeId && userId) {
      console.log(
        '💾 [DragTreeStore] Initiating database sync for node deletion...'
      )
      debouncedDbSync(async () => {
        try {
          // Mark all affected nodes as INACTIVE in database
          const inactivePromises = nodesToMarkInactive.map(id =>
            updateDragTreeNode({
              nodeId: id,
              status: DragTreeNodeStatus.INACTIVE,
            })
          )

          // Update tree structure
          const treeUpdatePromise = updateDragTree({
            treeId: dragTreeId,
            treeStructure: convertTreeToDbStructure(newTree),
          })

          // Execute all operations
          const results = await Promise.all([
            ...inactivePromises,
            treeUpdatePromise,
          ])

          const allSuccessful = results.every(result => result.success)
          if (allSuccessful) {
            console.log('✅ [DragTreeStore] Successfully synced node deletion')
          } else {
            console.error('❌ [DragTreeStore] Some sync operations failed')
            toast.error('Failed to fully sync deletion')
          }
        } catch (error) {
          console.error('💥 [DragTreeStore] Delete sync error:', error)
          toast.error('Failed to sync deletion')
        }
      })
    }
  },

  editNode: (nodeId, newLabel) => {
    console.log('✏️ [DragTreeStore] editNode called:', nodeId, newLabel)
    const { frontendTreeStructure, dragTreeId, userId } = get()
    if (!frontendTreeStructure) return

    // Edit node using utility function
    const newTree = editNodeInTree(frontendTreeStructure, nodeId, newLabel)
    if (!newTree) {
      console.warn('❌ [DragTreeStore] Failed to edit node')
      return
    }

    set({ frontendTreeStructure: newTree })
    set(state => {
      const newMap = new Map(state.nodeMap)
      const node = newMap.get(nodeId)
      if (node) {
        node.label = newLabel
        newMap.set(nodeId, node)
      }
      return { nodeMap: newMap }
    })
    toast.success(`Updated label to "${newLabel}"`)

    // Database sync with graceful error handling
    if (dragTreeId && userId) {
      console.log('💾 [DragTreeStore] Syncing label update for:', nodeId)
      debouncedDbSync(async () => {
        try {
          const nodeResult = await updateDragTreeNode({
            nodeId,
            label: newLabel,
          })

          if (nodeResult.success) {
            console.log('✅ [DragTreeStore] Successfully synced label update')
          } else {
            console.error('❌ [DragTreeStore] Failed to sync label update')
            toast.error('Failed to save changes')
          }
        } catch (error) {
          console.error('💥 [DragTreeStore] Edit sync error:', error)
          toast.error('Failed to save changes')
        }
      })
    }
  },

  reorderNode: (parentId, oldIndex, newIndex) => {
    const { frontendTreeStructure, dragTreeId } = get()
    if (!frontendTreeStructure) return

    const newTree = reorderNodeInTree(
      frontendTreeStructure,
      parentId,
      oldIndex,
      newIndex
    )

    if (newTree) {
      set({ frontendTreeStructure: newTree })
      if (dragTreeId) {
        debouncedDbSync(async () => {
          await updateDragTree({
            treeId: dragTreeId,
            treeStructure: convertTreeToDbStructure(newTree),
          })
        })
      }
    }
  },

  markNodeAsInterested: nodeId => {
    console.log('🔍 [DragTreeStore] markNodeAsInterested called with:', nodeId)
    const { frontendTreeStructure, dragTreeId, userId } = get()
    if (!frontendTreeStructure) return

    // Toggle interest using utility function
    const newTree = toggleNodeInterest(frontendTreeStructure, nodeId)
    if (!newTree) {
      console.warn('❌ [DragTreeStore] Failed to toggle node interest')
      return
    }

    set({ frontendTreeStructure: newTree })
    set(state => {
      const newMap = new Map(state.nodeMap)
      const updatedNode = get().findNodeById(nodeId)
      if (updatedNode) {
        newMap.set(nodeId, updatedNode)
      }
      return { nodeMap: newMap }
    })

    const nodeAfterUpdate = get().findNodeById(nodeId)
    const action = nodeAfterUpdate?.isInterestedIn
      ? 'marked as interested'
      : 'unmarked'
    console.log('🔍 [DragTreeStore] Node after update:', action)

    // Database sync with graceful error handling
    if (dragTreeId && userId) {
      const finalInterestStatus = nodeAfterUpdate?.isInterestedIn ?? false
      console.log(
        '💾 [DragTreeStore] Syncing interest status for:',
        nodeId,
        '-> isInterestedIn:',
        finalInterestStatus
      )
      debouncedDbSync(async () => {
        try {
          const result = await updateDragTreeNode({
            nodeId,
            isInterestedIn: finalInterestStatus,
          })

          if (result.success) {
            console.log(
              '✅ [DragTreeStore] Successfully synced interest status'
            )
          } else {
            console.error('❌ [DragTreeStore] Failed to sync interest status')
            toast.error('Failed to save interest status')
          }
        } catch (error) {
          console.error('💥 [DragTreeStore] Interest sync error:', error)
          toast.error('Failed to save interest status')
        }
      })
    }
  },

  resetAllInterest: () => {
    console.log('🔄 [DragTreeStore] resetAllInterest called')
    const { frontendTreeStructure } = get()
    if (!frontendTreeStructure) return

    // Reset all interest using utility function
    const newTree = resetAllInterestInTree(frontendTreeStructure)
    set({ frontendTreeStructure: newTree })
    // Since many nodes updated, rebuild the map once
    get().rebuildNodeMap()
    toast.success('All interest markers cleared')
  },

  getInterestedNodes: () => {
    const { frontendTreeStructure } = get()
    return collectInterestedNodes(frontendTreeStructure)
  },

  findNodeById: nodeId => {
    return get().nodeMap.get(nodeId) || null
  },

  getNodePath: nodeId => {
    const tree = get().frontendTreeStructure
    if (!tree) return ''
    return getFormattedNodePathFromTree(tree, nodeId)
  },

  addSimilarQuestions: (parentId, questionsMarkdown) => {
    console.log(
      '❓ [DragTreeStore] addSimilarQuestions called for parent:',
      parentId
    )
    const { frontendTreeStructure, dragTreeId } = get()
    if (!frontendTreeStructure || !dragTreeId) return

    const questions = parseQuestionsFromMarkdown(questionsMarkdown)
    if (questions.length === 0) return

    console.log('📝 [DragTreeStore] Parsed questions:', questions)

    const questionNodes = createNodesFromText(
      dragTreeId,
      questions,
      TreeNodeType.QUESTION
    )

    const newTree = addMultipleNodesToTree(
      frontendTreeStructure,
      parentId,
      questionNodes
    )
    if (!newTree) {
      console.warn('❌ [DragTreeStore] Failed to add question nodes')
      return
    }

    set({ frontendTreeStructure: newTree })
    set(state => {
      const newMap = new Map(state.nodeMap)
      questionNodes.forEach(node => newMap.set(node.id, node))
      return { nodeMap: newMap }
    })

    if (dragTreeId) {
      debouncedDbSync(async () => {
        await updateDragTree({
          treeId: dragTreeId,
          treeStructure: convertTreeToDbStructure(newTree),
        })
      })
    }
  },

  addSimilarCategories: (parentId, categoriesMarkdown) => {
    console.log(
      '📁 [DragTreeStore] addSimilarCategories called for parent:',
      parentId
    )
    const { frontendTreeStructure, dragTreeId } = get()
    if (!frontendTreeStructure || !dragTreeId) return

    // Convert markdown to tree nodes
    const subtree = markdownToTreeNode(categoriesMarkdown, 1)
    if (!subtree || subtree.children.length === 0) {
      console.warn('❌ [DragTreeStore] No valid categories found in markdown')
      return
    }

    console.log('📝 [DragTreeStore] Parsed categories:', subtree.children)

    // Add categories to tree using utility function
    const newTree = addMultipleNodesToTree(
      frontendTreeStructure,
      parentId,
      subtree.children
    )
    if (!newTree) {
      console.warn('❌ [DragTreeStore] Failed to add similar categories')
      return
    }

    set({ frontendTreeStructure: newTree })
    set(state => {
      const newMap = new Map(state.nodeMap)
      const addRecursively = (node: TreeNode) => {
        newMap.set(node.id, node)
        node.children.forEach(addRecursively)
      }
      subtree.children.forEach(addRecursively)
      return { nodeMap: newMap }
    })
    toast.success(`Added new categories with questions`)

    // TODO: Add database sync for similar categories
    console.log(
      '⚠️ [DragTreeStore] Database sync for similar categories not implemented yet'
    )
  },

  markNodeAsResearched: (nodeId: string) => {
    set(state => ({
      researchedNodeIds: new Set(state.researchedNodeIds).add(nodeId),
    }))
  },

  // Internal helper method to rebuild the flat map
  rebuildNodeMap: () => {
    const { frontendTreeStructure } = get()
    const newNodeMap = buildNodeMap(frontendTreeStructure)

    set({ nodeMap: newNodeMap })
    console.log(
      `🗂️ [DragTreeStore] Rebuilt node map with ${newNodeMap.size} nodes`
    )
  },

  // Node content methods - thread-safe for concurrent operations
  addNodeContent: (nodeId, contentItem) => {
    set(state => {
      const newNodeContent = new Map(state.nodeContent)
      const currentNodeContent = newNodeContent.get(nodeId) || new Map()
      const newNodeContentMap = new Map(currentNodeContent)

      newNodeContentMap.set(contentItem.contentId, contentItem)
      newNodeContent.set(nodeId, newNodeContentMap)

      console.log(
        `📝 [DragTreeStore] Added content ${contentItem.contentId} to node ${nodeId}`
      )
      return { nodeContent: newNodeContent }
    })
  },

  updateNodeContent: async (
    nodeId: string,
    contentId: string,
    updates: Partial<NodeContentItem>
  ) => {
    // Optimistically update the store for a responsive UI
    set(state => {
      const nodeContent = state.nodeContent.get(nodeId)
      if (!nodeContent) {
        console.warn(
          `[DragTreeStore] Attempted to update content for non-existent node: ${nodeId}`
        )
        return state
      }

      const currentContent = nodeContent.get(contentId)
      if (!currentContent) {
        console.warn(
          `[DragTreeStore] Attempted to update non-existent content: ${contentId}`
        )
        return state
      }

      const updatedContent = { ...currentContent, ...updates }
      const newNodeContentMap = new Map(nodeContent)
      newNodeContentMap.set(contentId, updatedContent)

      const newNodeContent = new Map(state.nodeContent)
      newNodeContent.set(nodeId, newNodeContentMap)

      console.log(
        `📝 [DragTreeStore] Optimistically updated content ${contentId} for node ${nodeId}`
      )

      return { nodeContent: newNodeContent }
    })

    // If the update includes text content, persist it to the database
    if (updates.contentText !== undefined) {
      try {
        // Inline server action - no separate function needed
        const { updateDragTreeNodeContent } = await import(
          '@/app/server-actions/drag-tree/research-update'
        )

        const result = await updateDragTreeNodeContent(
          contentId,
          updates.contentText
        )

        if (!result.success) {
          throw new Error(result.error || 'Failed to save content to database')
        }

        console.log(
          `✅ [DragTreeStore] Successfully persisted content ${contentId}`
        )
      } catch (error) {
        console.error(
          `❌ [DragTreeStore] Failed to persist content ${contentId}:`,
          error
        )
        // TODO: Implement error handling, e.g., show a toast, revert optimistic update
      }
    }
  },

  getNodeContent: nodeId => {
    return get().nodeContent.get(nodeId)
  },

  getSpecificContent: (nodeId, contentId) => {
    const nodeContentMap = get().nodeContent.get(nodeId)
    return nodeContentMap?.get(contentId)
  },

  // Lazy loading
  fetchNodeContent: async (nodeId: string, contentId: string) => {
    // Avoid fetching if content already loaded or currently loading
    const { nodeContent, contentFetchInProgress } = get()

    const nodeContentMap = nodeContent.get(nodeId)
    const currentContent = nodeContentMap?.get(contentId)

    if (currentContent && currentContent.contentText.trim().length > 0) {
      return
    }

    try {
      // Check if the content is already being fetched
      if (contentFetchInProgress.has(contentId)) {
        console.log(
          `[DragTreeStore] Content ${contentId} is already being fetched`
        )
        return
      }

      // Mark the content as being fetched
      contentFetchInProgress.add(contentId)

      const res = await fetch(`/api/dragtree/content?contentId=${contentId}`)
      const json = await res.json()
      if (json.success && json.data?.content_text) {
        get().updateNodeContent(nodeId, contentId, {
          contentText: json.data.content_text as string,
          metadata: json.data.content_metadata || {},
          messages: json.data.messages || [],
        })
      } else {
        console.error('[DragTreeStore] Failed to fetch content', json.error)
      }
    } catch (err) {
      console.error('[DragTreeStore] Error fetching content:', err)
    } finally {
      // Remove the content from being fetched
      contentFetchInProgress.delete(contentId)
    }
  },

  fetchNodeContentBatch: async pairs => {
    // Deduplicate by contentId
    const uniquePairs = pairs.reduce(
      (acc, p) => {
        if (!acc.some(a => a.contentId === p.contentId)) acc.push(p)
        return acc
      },
      [] as typeof pairs
    )

    const { contentFetchInProgress } = get()
    const toFetch = uniquePairs.filter(
      p => !contentFetchInProgress.has(p.contentId)
    )
    if (toFetch.length === 0) return

    // Mark as in progress
    toFetch.forEach(p => contentFetchInProgress.add(p.contentId))

    try {
      const { getNodeContentsBatch } = await import(
        '@/app/server-actions/drag-tree/get_node_contents_batch'
      )

      const data = await getNodeContentsBatch(toFetch.map(p => p.contentId))

      // Update store
      set(state => {
        const newNodeContent = new Map(state.nodeContent)
        data.forEach(item => {
          const nodeId = item.node_id
          const nodeMap = newNodeContent.get(nodeId) || new Map()
          nodeMap.set(item.id, {
            contentId: item.id,
            contentType: NodeContentType.QUICK_RESEARCH,
            contentVersion: 'v1',
            status: 'ACTIVE',
            contentText: item.content_text,
            metadata: item.content_metadata,
            messages: item.messages,
          })
          newNodeContent.set(nodeId, nodeMap)
        })
        return { nodeContent: newNodeContent }
      })
    } catch (err) {
      console.error('[DragTreeStore] Batch fetch failed', err)
    } finally {
      toFetch.forEach(p => contentFetchInProgress.delete(p.contentId))
    }
  },

  // Internal Utilities
  // debouncedRebuildNodeMap is attached after store creation; this is a temporary no-op for type safety
  debouncedRebuildNodeMap: () => {},

  isContentFetching: (contentId: string) => {
    const { contentFetchInProgress } = get()
    return contentFetchInProgress.has(contentId)
  },
}))

// Create debounced version of rebuildNodeMap outside the store to avoid recreating it
const createDebouncedRebuildNodeMap = (rebuildFn: () => void) =>
  debounce(rebuildFn, 50) // 50ms debounce - groups rapid updates

// Add debounced functionality to the store
const debouncedRebuildNodeMap = createDebouncedRebuildNodeMap(() =>
  useDragTreeStore.getState().rebuildNodeMap()
)

// Enhance the store state with the debounced method
useDragTreeStore.setState(state => ({
  ...state,
  debouncedRebuildNodeMap,
}))
