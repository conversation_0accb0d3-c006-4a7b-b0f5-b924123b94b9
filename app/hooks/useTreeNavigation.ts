'use client'

import { useEffect, useRef } from 'react'
import { TreeNode } from '@/app/types'
import { SCROLL_DELAY_MS } from '@/app/(conv)/dragTree/[dragTreeId]/components/HierarchicalOutline/constants'

type UseTreeNavigationProps = {
  treeTargetNodeId: string | null
  treeData: TreeNode | null
  expandNodePath: (nodePath: string[]) => void
}

/**
 * Utility function to find path to a node in the tree
 */
const findNodePath = (
  targetId: string,
  node: TreeNode,
  path: string[] = []
): string[] | null => {
  const currentPath = [...path, node.id]

  if (node.id === targetId) {
    return currentPath
  }

  for (const child of node.children) {
    const found = findNodePath(targetId, child, currentPath)
    if (found) {
      return found
    }
  }

  return null
}

/**
 * Scroll to a specific node element in the DOM with AbortController cleanup
 */
const scrollToNode = (nodeId: string, abortController: AbortController) => {
  const timeoutId = setTimeout(() => {
    if (!abortController.signal.aborted) {
      const targetElement = document.querySelector(`[data-node-id="${nodeId}"]`)
      if (targetElement) {
        targetElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'nearest',
        })
      }
    }
  }, SCROLL_DELAY_MS)

  // Listen for abort signal to clear timeout
  abortController.signal.addEventListener('abort', () => {
    clearTimeout(timeoutId)
  })
}

/**
 * Custom hook to handle tree navigation, particularly reverse navigation
 * from React Flow to Tree View
 */
export const useTreeNavigation = ({
  treeTargetNodeId,
  treeData,
  expandNodePath,
}: UseTreeNavigationProps): void => {
  // Use refs to avoid including them in useEffect dependencies
  const treeDataRef = useRef(treeData)
  const expandNodePathRef = useRef(expandNodePath)

  // Update refs when values change
  useEffect(() => {
    treeDataRef.current = treeData
  }, [treeData])

  useEffect(() => {
    expandNodePathRef.current = expandNodePath
  }, [expandNodePath])

  /**
   * Handle reverse navigation from React Flow to Tree View
   * Expands all parent nodes in the path and scrolls to target
   */
  useEffect(() => {
    if (!treeTargetNodeId || !treeDataRef.current) return

    console.log('🧭 Navigating to tree node:', treeTargetNodeId)

    // Create AbortController for cleanup
    const abortController = new AbortController()

    const nodePath = findNodePath(treeTargetNodeId, treeDataRef.current)

    if (nodePath) {
      console.log('📍 Found node path:', nodePath)

      // Expand all parent categories in the path (except the target node itself)
      expandNodePathRef.current(nodePath)

      // Scroll to the target node after DOM updates with cleanup
      scrollToNode(treeTargetNodeId, abortController)
    } else {
      console.warn('⚠️ Could not find path to target node:', treeTargetNodeId)
    }

    // Cleanup function to abort any pending operations
    return () => {
      abortController.abort()
    }
    // Only depend on treeTargetNodeId changes to prevent infinite loops
  }, [treeTargetNodeId])
}
