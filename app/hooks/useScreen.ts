import { useEffect } from 'react'
import { experimental_useObject as useObject } from '@ai-sdk/react'
import { screenSchema } from '@/app/api/rephrase/screen/utils'

type UseScreenProps = {
  description: string
  onScreenSuccess: (screenObject: object) => void
}

const useScreen = ({ description, onScreenSuccess }: UseScreenProps) => {
  const {
    object,
    submit,
    isLoading: isLoadingScreen,
  } = useObject({
    api: '/api/rephrase/screen',
    schema: screenSchema,
    onFinish: result => {
      if (result.object) {
        onScreenSuccess(result.object)
      }
    },
  })

  useEffect(() => {
    if (object) {
      onScreenSuccess(object)
    }
  }, [object, onScreenSuccess])

  // useEffect(() => {
  //   if (messages.length > 0) {
  //     const lastMessage = messages[messages.length - 1]?.content;
  //     if (lastMessage) {
  //       const parsed = [
  //         lastDescription,
  //         ...extractNumberedSuggestions(lastMessage),
  //       ];
  //       onRephraseSuccess(parsed);
  //     }
  //   }
  // }, [messages, onRephraseSuccess, description, lastDescription]);

  const handleScreen = () => {
    submit(description)
  }

  return {
    handleScreen,
    isLoading: isLoadingScreen,
  }
}

export default useScreen
