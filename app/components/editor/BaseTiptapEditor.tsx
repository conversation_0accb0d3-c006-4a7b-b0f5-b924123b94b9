'use client'

// Workaround for isSpace error with markdown-it and Turbopack
if (
  typeof window !== 'undefined' &&
  typeof (window as any).isSpace === 'undefined'
) {
  ;(window as any).isSpace = function (code: number) {
    return code === 0x20 || code === 0x09
  }
}

import { useState, useEffect, useCallback } from 'react'
import {
  Editor,
  JSONContent,
  EditorContent as TiptapEditorContent,
  useEditor,
} from '@tiptap/react'
import { EditorBubbleMenu, TableBubbleMenu } from './components'
import { TiptapExtensions } from './extensions'
import { TiptapEditorProps } from './props'
import debounce from 'lodash/debounce'
import { cn } from '@/lib/utils'
import { marked } from 'marked'
import { generateJSON } from '@tiptap/html'
import DOMPurify from 'isomorphic-dompurify'

// Content detection utilities
export const isJSONContent = (content: string): boolean => {
  if (!content || typeof content !== 'string') return false
  try {
    const parsed = JSON.parse(content)
    return typeof parsed === 'object' && parsed !== null
  } catch {
    return false
  }
}

export const cleanMermaidJSON = (json: JSONContent): JSONContent => {
  if (!json.content || !Array.isArray(json.content)) return json

  return {
    ...json,
    content: json.content.map((node: JSONContent) => {
      // Skip non-mermaidComponent node
      if (node.type !== 'mermaidComponent') return node

      // Clean mermaidComponent node
      const { attrs, content } = node
      // If code is null, but content is not empty, set code to the text of the first child
      // Remove content to save space
      if (attrs?.code === null && content?.[0]?.content?.[0]?.text) {
        const { content, ...restNode } = node
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const _unusedContent = content
        return {
          ...restNode,
          attrs: { ...attrs, code: content?.[0]?.content?.[0]?.text || '' },
        }
      }
      return node
    }),
  }
}

// Convert markdown to initial content for Tiptap
export const convertMarkdownToTiptapContent = (
  markdown: string
): JSONContent => {
  // For now, simple conversion - will be enhanced as needed
  return {
    type: 'doc',
    content: [
      {
        type: 'paragraph',
        content: [
          {
            type: 'text',
            text: markdown,
          },
        ],
      },
    ],
  }
}

// Preprocess markdown to convert mermaid blocks to HTML divs
const preprocessMermaidMarkdown = (markdown: string): string => {
  if (!markdown || typeof markdown !== 'string') return markdown

  // Replace ```mermaid code blocks with our custom div elements
  return markdown.replace(/```mermaid\n([\s\S]*?)\n```/g, (_match, code) => {
    const trimmedCode = code.trim()
    const encoded = encodeURIComponent(trimmedCode)
    return `<div data-type="mermaid-block" data-mermaid-code="${encoded}"></div>`
  })
}

// Strip an outermost triple-backtick code fence (e.g., ```markdown ... ```) so that
// TipTap treats the inner content as normal Markdown instead of a single code block.
// It preserves any inner fenced code blocks.
const stripOuterCodeFence = (markdown: string): string => {
  if (!markdown || typeof markdown !== 'string') return markdown

  // Regex breakdown:
  // ^``` optional_lang\n  => opening fence at start of string with optional language tag
  // ([\s\S]*?)         => lazily capture everything until...
  // \n```$              => closing fence at end of string
  const fenceRegex = /^```(?:\s*([a-zA-Z0-9_-]+)?\s*)?\n([\s\S]*?)\n```$/

  const match = markdown.match(fenceRegex)
  if (match) {
    // Return the inner content, trimming a trailing newline if present
    return match[2].replace(/\n$/, '')
  }

  return markdown
}

// Process content based on type (JSON vs markdown)
export const processContentForEditor = (
  content: string
): JSONContent | string => {
  if (!content) return ''

  if (isJSONContent(content)) {
    try {
      return JSON.parse(content) as JSONContent
    } catch {
      return content
    }
  }

  // If it's markdown, preprocess mermaid blocks and convert to Tiptap JSON
  // 1. Remove outermost ```markdown fences if entire content is wrapped
  const stripped = stripOuterCodeFence(content)

  // 2. Then handle special mermaid code blocks
  const preprocessed = preprocessMermaidMarkdown(stripped)
  try {
    // Convert markdown → HTML → Tiptap JSON for correct initial rendering
    const rawHTML = marked.parse(preprocessed) as string
    const html = DOMPurify.sanitize(rawHTML)
    const json = generateJSON(html, TiptapExtensions)
    return json as JSONContent
  } catch {
    // Fallback to returning raw preprocessed markdown if conversion fails
    return preprocessed
  }
}

/**
 * Fast deep equality check optimized for ProseMirror/Tiptap JSON documents
 * More efficient than JSON.stringify for nested objects with consistent key ordering
 */
const fastDeepEqual = (a: any, b: any): boolean => {
  // Quick reference check first - O(1)
  if (a === b) return true

  // Type checks
  if (typeof a !== typeof b) return false
  if (a == null || b == null) return false
  if (typeof a !== 'object') return a === b

  // Array handling
  if (Array.isArray(a)) {
    if (!Array.isArray(b) || a.length !== b.length) return false
    for (let i = 0; i < a.length; i++) {
      if (!fastDeepEqual(a[i], b[i])) return false
    }
    return true
  }

  // Object handling - check key counts first
  const keysA = Object.keys(a)
  const keysB = Object.keys(b)
  if (keysA.length !== keysB.length) return false

  // Check each key-value pair
  for (const key of keysA) {
    if (!(key in b)) return false
    if (!fastDeepEqual(a[key], b[key])) return false
  }

  return true
}

export type BaseTiptapEditorProps = {
  content?: string
  placeholder?: string
  editable?: boolean
  showBubbleMenu?: boolean
  onUpdate?: (content: JSONContent) => void
  onTextUpdate?: (markdown: string) => void
  debounceMs?: number
  className?: string
  extensions?: any[]
  editorProps?: any
  autofocus?: boolean | 'start' | 'end'
  immediatelyRender?: boolean
}

export type BaseTiptapEditorReturn = {
  editor: Editor | null
  setContent: (content: string | JSONContent) => void
  getContent: () => JSONContent | null
  getMarkdown: () => string
  isEditable: boolean
  setEditable: (editable: boolean) => void
}

export const useBaseTiptapEditor = ({
  content = '',
  placeholder = "Press '/' for commands",
  editable = true,
  onUpdate,
  onTextUpdate,
  debounceMs = 1000,
  extensions = TiptapExtensions,
  editorProps = TiptapEditorProps,
  autofocus = false,
  immediatelyRender = false,
}: BaseTiptapEditorProps): BaseTiptapEditorReturn => {
  const [isEditable, setIsEditable] = useState<boolean>(editable)

  // Create debounced update functions
  const debouncedOnUpdate = useCallback(
    debounce((content: JSONContent) => {
      onUpdate?.(content)
    }, debounceMs),
    [onUpdate, debounceMs]
  )

  const debouncedOnTextUpdate = useCallback(
    debounce((markdown: string) => {
      onTextUpdate?.(markdown)
    }, debounceMs),
    [onTextUpdate, debounceMs]
  )

  // Configure extensions with custom placeholder if provided
  const configuredExtensions = extensions.map(ext => {
    if (
      ext.name === 'placeholder' &&
      placeholder !== "Press '/' for commands"
    ) {
      return ext.configure({
        placeholder: () => placeholder,
        includeChildren: true,
      })
    }
    return ext
  })

  const editor = useEditor({
    extensions: configuredExtensions,
    editorProps: {
      ...editorProps,
      attributes: {
        ...editorProps.attributes,
        class: `prose prose-sm prose-stone dark:prose-invert prose-headings:font-display font-default focus:outline-none max-w-full text-sm`,
        style:
          'width: 100%; max-width: 100%; min-width: 0; box-sizing: border-box; overflow-x: hidden;',
      },
    },
    editable: isEditable,
    content: processContentForEditor(content),
    onUpdate: ({ editor }) => {
      if (onUpdate) {
        const cleanedJSON = cleanMermaidJSON(editor.getJSON())
        debouncedOnUpdate(cleanedJSON)
      }

      if (onTextUpdate) {
        const markdown = editor.storage.markdown.getMarkdown()
        debouncedOnTextUpdate(markdown)
      }
    },
    autofocus,
    immediatelyRender,
  })

  // Update editor content when prop changes.
  //
  // IMPORTANT: We must NOT call `editor.commands.setContent` while the user is
  // actively typing (i.e. the editor is in editable mode). Doing so replaces
  // the document and resets the selection, causing the cursor to jump to the end.
  //
  // This addresses the "cursor-jumps-to-end" issue described in:
  // https://github.com/facebook/react/issues/14904
  useEffect(() => {
    if (editor && content !== undefined) {
      const processedContent = processContentForEditor(content)
      const currentContent = editor.getJSON()

      // Skip update if editor is currently editable (user is typing)
      const isEditable = editor.isEditable
      if (isEditable) {
        return
      }

      // Only update if content actually changed using fast deep equality
      // This is more efficient than JSON.stringify for nested ProseMirror documents
      if (!fastDeepEqual(currentContent, processedContent)) {
        editor.commands.setContent(processedContent)
      }
    }
  }, [content, editor])

  // Update editable state when prop changes
  useEffect(() => {
    if (editor && isEditable !== editable) {
      setIsEditable(editable)
      editor.setEditable(editable)
    }
  }, [editable, editor, isEditable])

  const setContent = useCallback(
    (newContent: string | JSONContent) => {
      if (editor) {
        if (typeof newContent === 'string') {
          const processedContent = processContentForEditor(newContent)
          editor.commands.setContent(processedContent)
        } else {
          editor.commands.setContent(newContent)
        }
      }
    },
    [editor]
  )

  const getContent = useCallback(() => {
    return editor ? editor.getJSON() : null
  }, [editor])

  const getMarkdown = useCallback(() => {
    return editor ? editor.storage.markdown.getMarkdown() : ''
  }, [editor])

  const setEditable = useCallback(
    (newEditable: boolean) => {
      setIsEditable(newEditable)
      if (editor) {
        editor.setEditable(newEditable)
      }
    },
    [editor]
  )

  return {
    editor,
    setContent,
    getContent,
    getMarkdown,
    isEditable,
    setEditable,
  }
}

export const BaseTiptapEditor: React.FC<
  BaseTiptapEditorProps & {
    showBubbleMenu?: boolean
    className?: string
  }
> = ({ showBubbleMenu = true, className = '', ...editorProps }) => {
  const { editor } = useBaseTiptapEditor(editorProps)

  return (
    <div className={cn('relative w-full', className)}>
      {showBubbleMenu && editor && (
        <>
          <EditorBubbleMenu editor={editor} />
          <TableBubbleMenu editor={editor} />
        </>
      )}
      <TiptapEditorContent editor={editor} />
    </div>
  )
}

export default BaseTiptapEditor
