import { SubscriptionTier } from '@prisma/client'
import prisma from '@/app/libs/prismadb'
import type { Session } from 'next-auth'

export const DEFAULT_MAX_PROMPT_CHARS = 1_000_000
import {
  LLM_MODEL_CONFIG,
  DEFAULT_MODEL_CONFIG,
  ModelConfig,
  APIRoute,
} from '@/app/config/llm-models'

/**
 * Cache for user subscription tiers to avoid repeated database queries
 * Cache expires after 5 minutes to ensure fresh data
 */
const userTierCache = new Map<
  string,
  { tier: SubscriptionTier; timestamp: number }
>()
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes in milliseconds

/**
 * Fetches user's subscription tier from database with caching
 * @param userId - The user's ID
 * @returns Promise<SubscriptionTier> - The user's subscription tier
 */
export async function getUserSubscriptionTier(
  userId: string
): Promise<SubscriptionTier> {
  try {
    // Check cache first
    const cached = userTierCache.get(userId)
    const now = Date.now()

    if (cached && now - cached.timestamp < CACHE_DURATION) {
      return cached.tier
    }

    // Fetch from database
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { subscription_tier: true },
    })

    const tier = user?.subscription_tier || SubscriptionTier.FREE

    // Update cache
    userTierCache.set(userId, { tier, timestamp: now })

    return tier
  } catch (error) {
    console.error('🔍 Error fetching user subscription tier:', error)
    // Return FREE tier as fallback
    return SubscriptionTier.FREE
  }
}

/**
 * Gets the appropriate model configuration for a user and API route
 * @param userId - The user's ID
 * @param apiRoute - The API route requesting the model config
 * @returns Promise<ModelConfig> - The model configuration to use
 */
export async function getModelConfig(
  userId: string,
  apiRoute: APIRoute
): Promise<ModelConfig> {
  try {
    const userTier = await getUserSubscriptionTier(userId)

    // Get configuration for the user's tier and route
    const config = LLM_MODEL_CONFIG[userTier]?.[apiRoute]

    if (!config) {
      console.warn(
        `⚠️ No model config found for tier ${userTier}, route ${apiRoute}. Using default.`
      )
      return DEFAULT_MODEL_CONFIG
    }

    console.log(
      `🤖 Model config for user ${userId} (${userTier}), route ${apiRoute}: ${config.model} (${config.model_provider})`
    )

    return config
  } catch (error) {
    console.error('🔍 Error getting model config:', error)
    console.log(`🤖 Falling back to default model config for route ${apiRoute}`)
    return DEFAULT_MODEL_CONFIG
  }
}

/**
 * Gets the model configuration synchronously if the user tier is already known
 * Useful when you already have the user's subscription tier
 * @param userTier - The user's subscription tier
 * @param apiRoute - The API route requesting the model config
 * @returns ModelConfig - The model configuration to use
 */
export function getModelConfigSync(
  userTier: SubscriptionTier,
  apiRoute: APIRoute
): ModelConfig {
  try {
    const config = LLM_MODEL_CONFIG[userTier]?.[apiRoute]

    if (!config) {
      console.warn(
        `⚠️ No model config found for tier ${userTier}, route ${apiRoute}. Using default.`
      )
      return DEFAULT_MODEL_CONFIG
    }

    return config
  } catch (error) {
    console.error('🔍 Error getting model config sync:', error)
    return DEFAULT_MODEL_CONFIG
  }
}

/**
 * Gets the model configuration using a NextAuth session to avoid an extra DB read.
 */
export async function getModelConfigForSession(
  session: Session | null,
  apiRoute: APIRoute
): Promise<ModelConfig> {
  if (session?.user?.subscription_tier) {
    return getModelConfigSync(session.user.subscription_tier, apiRoute)
  }
  if (session?.user?.id) {
    return getModelConfig(session.user.id, apiRoute)
  }
  return DEFAULT_MODEL_CONFIG
}

/**
 * Clears the user tier cache for a specific user
 * Useful when user subscription changes
 * @param userId - The user's ID
 */
export function clearUserTierCache(userId?: string): void {
  if (userId) {
    userTierCache.delete(userId)
    console.log(`🗑️ Cleared tier cache for user ${userId}`)
  } else {
    userTierCache.clear()
    console.log('🗑️ Cleared entire tier cache')
  }
}

/**
 * Gets cache statistics for monitoring
 * @returns Object with cache size and oldest entry age
 */
export function getTierCacheStats(): { size: number; oldestEntryAge: number } {
  const now = Date.now()
  let oldestAge = 0

  userTierCache.forEach(value => {
    const age = now - value.timestamp
    if (age > oldestAge) {
      oldestAge = age
    }
  })

  return {
    size: userTierCache.size,
    oldestEntryAge: oldestAge,
  }
}

/**
 * Type guard to check if a string is a valid API route
 * @param route - String to check
 * @returns boolean - True if the string is a valid API route
 */
export function isValidAPIRoute(route: string): route is APIRoute {
  const validRoutes: APIRoute[] = [
    'dragtree_generate_questions',
    'dragtree_research_generate',
    'dragtree_generate_similar_questions',
    'dragtree_generate_similar_categories',
    'screening_diagnose',
    'screening_rephrase',
    'aipane_generate',
    'aipane_chat',
  ]

  return validRoutes.includes(route as APIRoute)
}
