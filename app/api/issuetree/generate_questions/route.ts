import { streamText } from 'ai'
import { NextResponse } from 'next/server'
import { IssueTreeStatus, OpenAIUsageType, IssueTree } from '@prisma/client'
import { logAPIUsage } from '@/app/api/utils'
import { azure } from '@ai-sdk/azure'

// IMPORTANT! Set the runtime to edge
export const runtime = 'edge'

const baseUrl =
  process.env.NODE_ENV === 'development'
    ? 'http://localhost:3000'
    : process.env.NEXT_PUBLIC_SERVER_URL

export async function POST(req: Request) {
  // TODO: Re-implement with AI SDK 5 - disabled for now to unblock core features
  return new Response(
    JSON.stringify({ error: 'Issue tree generation temporarily disabled' }),
    {
      status: 503,
      headers: { 'Content-Type': 'application/json' },
    }
  )

  try {
    // Extract the `messages`, `conversationId`, and `currentUser` from the body of the request
    const { messages, conversationId, currentUser, issueTreeId } =
      await req.json()

    console.log(
      'api/issuetree/generate_questions:',
      messages,
      conversationId,
      currentUser
    )

    const formattedSystemMessage = await fetchSystemPrompt(
      conversationId,
      currentUser
    )
    if (!formattedSystemMessage) {
      return new NextResponse('Error fetching system prompt', { status: 500 })
    }

    const model_name = 'gpt-4o-mini'
    // process.env.NODE_ENV === "development" ? "gpt-4o-mini" : "gpt-4o";
    console.log('model_name', model_name)

    const result = streamText({
      model: azure(model_name),
      messages: [
        {
          role: 'system',
          content: formattedSystemMessage || '',
        },
      ],
      onFinish: async ({ text }) => {
        console.log('api/issuetree/generate_questions: completion')
        console.log(OpenAIUsageType.ISSUE_TREE_GENERATE_QUESTIONS)
        await Promise.all([
          // record the API usage to the database as audit log
          logAPIUsage({
            open_ai_usage_type: OpenAIUsageType.ISSUE_TREE_GENERATE_QUESTIONS,
            model_name: model_name,
            input_text: formattedSystemMessage || '',
            output_text: text,
            currentUser: currentUser,
            conversationId: conversationId,
          }),

          // Save the completion to the database
          updateTreeContent({
            currentUser: currentUser,
            issueTreeId: issueTreeId,
            raw_markdown: text,
          }),
        ])
      },
    })

    // Respond with the stream
    return result.toUIMessageStreamResponse()
  } catch (error) {
    // Log any errors to the console and return a 500 error response
    console.log('api/issuetree/generate_questions: error:', error)
    return new NextResponse('Error processing request', { status: 500 })
  }
}

// Helper functions

async function fetchSystemPrompt(
  conversationId: string,
  currentUser: any
): Promise<string | null> {
  try {
    const response = await fetch(`${baseUrl}/api/issuetree/load`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ conversationId, currentUser, isReturnAll: true }),
    })

    if (!response.ok) {
      throw new Error(`Server responded with status ${response.status}`)
    }

    const data: IssueTree = await response.json()
    console.log('formattedSystemMessage', data.prompt)
    return data.prompt
  } catch (error) {
    console.error('Error fetching system prompt:', error)
    return null
  }
}

async function updateTreeContent({
  currentUser,
  issueTreeId,
  raw_markdown,
}: {
  currentUser: any
  issueTreeId: string
  raw_markdown: string
}): Promise<void> {
  try {
    const response = await fetch(`${baseUrl}/api/issuetree/update`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        currentUser: currentUser,
        issueTreeId: issueTreeId,
        raw_markdown: raw_markdown,
        status: IssueTreeStatus.ACTIVE,
      }),
    })

    if (!response.ok) {
      throw new Error('Server response: ' + response.status)
    }

    const result = await response.json()
    console.log('updateTreeContent response:', result)
  } catch (error) {
    console.error('Error in updateTreeContent:', error)
  }
}
