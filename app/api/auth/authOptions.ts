import { AuthOptions, DefaultSession } from 'next-auth'
import Gith<PERSON><PERSON>rovider from 'next-auth/providers/github'
import GoogleProvider from 'next-auth/providers/google'
import CredentialsProvider from 'next-auth/providers/credentials'
import { PrismaAdapter } from '@auth/prisma-adapter'
import type { Adapter } from 'next-auth/adapters'

import prisma from '@/app/libs/prismadb'
import { UserStatus, SubscriptionTier } from '@prisma/client'

// Extend the built-in session types
declare module 'next-auth' {
  interface Session extends DefaultSession {
    user: {
      id: string
      status: UserStatus
      subscription_tier: SubscriptionTier
    } & DefaultSession['user']
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string
    status: UserStatus
    subscription_tier: SubscriptionTier
  }
}

export const authOptions: AuthOptions = {
  adapter: PrismaAdapter(prisma) as Adapter,
  providers: [
    GithubProvider({
      clientId: process.env.GITHUB_ID as string,
      clientSecret: process.env.GITHUB_SECRET as string,
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
    }),
  ],
  debug: process.env.NODE_ENV === 'development',
  session: {
    strategy: 'jwt',
    maxAge: 7 * 24 * 60 * 60, // 7 days in seconds
  },
  callbacks: {
    signIn: async ({ user }) => {
      // Check if user exists and is active
      const dbUser = await prisma.user.findUnique({
        where: { email: user.email as string },
        select: { status: true },
      })

      // Allow sign-in for new users or active existing users
      return !dbUser || dbUser.status === UserStatus.ACTIVE
    },

    jwt: async ({ token, user, trigger, session }) => {
      if (user) {
        // Fetch user data from database
        const dbUser = await prisma.user.findUnique({
          where: { email: user.email as string },
          select: { id: true, status: true, subscription_tier: true },
        })

        // Add custom claims to the token
        if (dbUser) {
          token.id = dbUser.id
          token.status = dbUser.status
          token.subscription_tier = dbUser.subscription_tier
        }
      }

      // Handle manual session.update() calls – refresh subscription tier
      if (trigger === 'update') {
        if (
          session?.subscription_tier &&
          session.subscription_tier !== token.subscription_tier
        ) {
          token.subscription_tier =
            session.subscription_tier as SubscriptionTier
        } else if (token?.id) {
          // fetch latest tier if not provided
          try {
            const refreshedUser = await prisma.user.findUnique({
              where: { id: token.id as string },
              select: { subscription_tier: true },
            })
            if (refreshedUser?.subscription_tier) {
              token.subscription_tier = refreshedUser.subscription_tier
            }
          } catch (err) {
            console.error(
              'Failed to refresh subscription tier during JWT update',
              err
            )
          }
        }
      }

      return token
    },

    session: ({ session, token }) => {
      // Add custom session data
      if (session.user) {
        session.user.id = token.id as string
        session.user.status = token.status as UserStatus
        session.user.subscription_tier =
          token.subscription_tier as SubscriptionTier
      }
      return session
    },
  },
  secret: process.env.NEXTAUTH_SECRET,
}

// Add credentials provider only for non-production environments
if (process.env.NODE_ENV !== 'production') {
  authOptions.providers.push(
    CredentialsProvider({
      name: 'E2E Test Credentials',
      credentials: {
        email: {
          label: 'Email',
          type: 'text',
          placeholder: '<EMAIL>',
        },
      },
      async authorize(credentials) {
        if (!credentials?.email) {
          throw new Error('Email is required for E2E login.')
        }

        const user = await prisma.user.findUnique({
          where: { email: credentials.email },
        })

        if (user) {
          // Return user object to create a session
          return user
        } else {
          // If you want to auto-create a user, you can do it here
          return null
        }
      },
    })
  )
}
