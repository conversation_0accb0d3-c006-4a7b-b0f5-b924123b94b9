import {
  streamText,
  createUIMessageStream,
  createUIMessageStreamResponse,
} from 'ai'
import { azure } from '@ai-sdk/azure'
import { AIPaneChatRequest } from './types'
import type { Msg } from '@/app/types/ai-sdk5'
import { createMessageMetadata } from '@/app/types/ai-sdk5'
import { createAIUsage } from '@/app/server-actions/log_ai_usage'
import { AIUsageType } from '@prisma/client'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/authOptions'
import {
  persistConversationTurn,
  ExecutionStepCollector,
  addMessageToConversation,
} from '@/app/server-actions/ai-chat'
import {
  buildSearchTools,
  type SearchProgressCallback,
} from '@/app/api/dragtree/shared/search-tools'
import { type SearchMetadata } from '@/app/api/dragtree/shared/brave-search'
import { isRateLimited, getRetryAfterSeconds } from '@/app/libs/rateLimiter'
import { standardErrors } from '@/app/api/aipane/shared/errors'
import { buildContextMessages } from '@/app/api/aipane/assistant/utils'
import { AiChatLogger } from '@/app/server-actions/ai-chat/logging'
import { getModelConfig } from '@/app/libs/model-config'
import {
  getLLMLanguageName,
  type SupportedLanguageCode,
} from '@/app/(conv)/screening/constants/languages'
import prisma from '@/app/libs/prismadb'

// Import configuration constants
import { RATE_LIMIT_CONFIG, RETRY_CONFIG } from './chat-config'
// Note: persistTurn helper removed due to API incompatibility

export const maxDuration = 60

// Initialize structured logger
const chatLogger = new AiChatLogger('aipane-chat-route')

export async function POST(req: Request) {
  try {
    // Retrieve the authenticated user session to obtain userId for logging
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return standardErrors.unauthorized()
    }
    const userId = session.user.id

    // Rate limiting - using config constants
    const rateLimitKey = `${userId}:aipane:chat`
    if (isRateLimited(rateLimitKey, RATE_LIMIT_CONFIG.WINDOW_MS)) {
      const retryAfter = getRetryAfterSeconds(
        rateLimitKey,
        RATE_LIMIT_CONFIG.WINDOW_MS
      )
      return standardErrors.rateLimited(
        retryAfter,
        RATE_LIMIT_CONFIG.MAX_REQUESTS.toString()
      )
    }

    // Parse request body with proper typing
    let requestData: AIPaneChatRequest
    try {
      const rawData = await req.json()
      requestData = rawData as AIPaneChatRequest
    } catch (error) {
      console.error('🤖 [Chat Real] Failed to parse JSON:', error)
      return standardErrors.invalidJson()
    }

    if (!requestData) {
      return standardErrors.missingBody()
    }

    chatLogger.info('Received chat request', {
      userId,
    })

    // Extract and validate request data
    const rawMessages = requestData.messages

    // Get model configuration based on user's subscription tier
    const modelConfig = await getModelConfig(userId, 'aipane_chat')
    const model = modelConfig.model

    const context = requestData.context
    const settings = requestData.settings || {}
    let conversationId = requestData.conversationId
    const contextEntityType = requestData.contextEntityType
    const contextEntityId = requestData.contextEntityId
    const contextIds = requestData.contextIds || [] // DEPRECATED: No longer used for context, only stored in metadata

    // Validate raw messages
    if (!rawMessages || !Array.isArray(rawMessages)) {
      return standardErrors.invalidMessages()
    }

    // Use the extracted content normalization utility
    // The Vercel AI SDK v2 sends message.content as an *array* of parts to
    // support future multimodal messages. We normalize to string format.
    const { normaliseMessageContent } = await import(
      '@/app/libs/ai-chat/content'
    )

    const { extractTextContent } = await import('@/app/types/ai-sdk5')

    const messages = rawMessages.map(m => {
      const primary = normaliseMessageContent((m as any).content)
      const fallback = extractTextContent(m as any)
      return {
        ...m,
        content: primary || fallback,
      }
    })

    // ConversationId is mandatory now to avoid duplicate rows
    if (!conversationId || !conversationId.startsWith('thread_')) {
      return standardErrors.invalidIdFormat('conversationId', 'thread_*')
    }

    // Upsert conversation row if it doesn't exist yet (idempotent)
    await prisma.aiConversation.upsert({
      where: { id: conversationId },
      create: {
        id: conversationId,
        userId: session.user.id,
        title: 'Untitled chat', // Will be auto-generated after first turn
        contextEntityType: contextEntityType || 'drag_tree',
        contextEntityId: contextEntityId || '',
        metadata: { contextIds: contextIds || [] }, // DEPRECATED: Stored but not used - system uses directContext
      },
      update: {}, // Nothing to update on repeat calls
    })

    // Validate messages
    if (!Array.isArray(messages) || messages.length === 0) {
      return standardErrors.invalidMessages(
        'Messages array must contain at least one message'
      )
    }

    chatLogger.info('Processing chat request', {
      userId,
    })

    // Fetch language preference if context is a drag tree
    let preferredLanguage: SupportedLanguageCode = 'en'
    if (contextEntityType === 'drag_tree' && contextEntityId) {
      try {
        const dragTree = await prisma.dragTree.findUnique({
          where: { id: contextEntityId },
          select: { preferred_language: true },
        })
        if (dragTree?.preferred_language) {
          preferredLanguage =
            dragTree.preferred_language as SupportedLanguageCode
        }
      } catch (error) {
        console.warn('Failed to fetch drag tree language preference:', error)
      }
    }

    const languageName = getLLMLanguageName(preferredLanguage)

    // (auto-creation removed – must be created via /conversations)

    // Prepare messages for AI model using V2 approach
    const latestUser = messages[messages.length - 1]
    // Build context messages with message count limits instead of token limits
    // Uses directContext (text from request) - no document ID system needed
    const contextResult = await buildContextMessages(
      conversationId,
      {
        role: 'user',
        content: latestUser.content,
      },
      context && context.trim().length > 0 ? context : undefined,
      `Please respond in ${languageName}.`
    )

    if (!contextResult.success) {
      return standardErrors.internalError(
        contextResult.error || 'Failed to build context'
      )
    }

    const { messages: modelMessages } = contextResult.data!

    // V2 already provides properly ordered messages with merged system prompt and language instruction

    // Initialize execution step collector and search metadata
    const stepCollector = new ExecutionStepCollector()
    const searchMetadata: SearchMetadata[] = []
    let finalResponse = ''

    // Real-time step streaming implementation

    // Create search progress callback for logging only
    // Note: Step collection is handled by onChunk to avoid duplicates
    const searchProgressCallback: SearchProgressCallback = _status => {
      chatLogger.debug('Search progress update', { userId })
    }

    // Use createUIMessageStream to properly handle data parts and text streaming
    const stream = createUIMessageStream<Msg>({
      async execute({ writer }) {
        // Stream the LLM response with tool execution
        const result = streamText({
          model: azure(model),
          messages: modelMessages,
          tools: buildSearchTools(searchMetadata, searchProgressCallback),
          // Note: maxSteps not available in this AI SDK version
          temperature: modelConfig.temperature,
          maxOutputTokens: modelConfig.maxOutputTokens,
          // Stream tool calls in real-time
          onChunk: async chunk => {
            // Capture tool calls and add them to the execution step collector
            if (chunk.chunk.type === 'tool-call') {
              // Add raw step data to collector
              stepCollector.addStep({
                providerType: chunk.chunk.type,
                metadata: { raw: chunk.chunk },
              })

              // Stream execution step data to client
              // Execution step streaming disabled temporarily
            }

            if (chunk.chunk.type === 'tool-result') {
              // Add raw step data to collector
              stepCollector.addStep({
                providerType: chunk.chunk.type,
                metadata: { raw: chunk.chunk },
              })

              // Stream execution step result to client
              // Execution step streaming disabled temporarily
            }
          },
          onFinish: async result => {
            // CRITICAL: This is where we do atomic persistence
            try {
              chatLogger.info('Stream finished, starting atomic persistence', {
                userId,
              })

              // Notify client that streaming is complete
              // Stream finish notification disabled temporarily

              // Get the latest user message
              const userMessages = messages.filter(m => m.role === 'user')
              const latestUserMessage =
                userMessages[userMessages.length - 1]?.content || ''

              // Use the clean content directly from the result
              finalResponse = result.text

              // Add reasoning summary if there were any tool calls or steps
              if (stepCollector.getStepCount() > 0) {
                const summary = `Generated response using ${searchMetadata.length} web search(es) and ${stepCollector.getStepCount()} execution step(s)`
                stepCollector.addStep({
                  providerType: 'reasoning',
                  metadata: {
                    raw: {
                      type: 'reasoning',
                      summary,
                      timestamp: Date.now(),
                    },
                  },
                })

                // Note: Reasoning summary step added to collector for persistence
              }

              // Log execution summary
              chatLogger.info('Execution summary', {
                userId,
              })

              // Persist the complete conversation turn atomically with basic retry logic
              const MAX_RETRIES = RETRY_CONFIG.MAX_RETRIES
              let attempt = 0
              let persistResult: Awaited<
                ReturnType<typeof persistConversationTurn>
              > | null = null

              const isFirstTurn = messages.length === 1 // only the new user msg

              // Persist context once at the start of the conversation so that
              // future calls automatically receive it via buildContextMessages.
              if (isFirstTurn && context && context.trim().length > 0) {
                try {
                  await addMessageToConversation(conversationId!, {
                    role: 'SYSTEM',
                    content: `CONTEXT:\n${context}`,
                  })
                  chatLogger.info('Persisted context message', { userId })
                } catch (_ctxErr) {
                  chatLogger.warn('Failed to persist context message', {
                    userId,
                  })
                }
              }

              while (attempt < MAX_RETRIES) {
                attempt++
                // eslint-disable-next-line no-await-in-loop
                persistResult = await persistConversationTurn({
                  conversationId: conversationId!,
                  userMessage: {
                    role: 'USER',
                    content: latestUserMessage,
                  },
                  assistantMessage: {
                    role: 'ASSISTANT',
                    content: finalResponse,
                    steps: stepCollector.getSteps(),
                    metadata: createMessageMetadata(
                      {
                        id: `msg-${Date.now()}`,
                        role: 'assistant',
                        content: finalResponse,
                        parts: [{ type: 'text', text: finalResponse }],
                      } as Msg,
                      result.usage,
                      {
                        hasContext: !!context,
                        searchResults: searchMetadata.length,
                        executionSteps: stepCollector.getStepCount(),
                      }
                    ),
                  },
                })

                if (persistResult.success) break

                chatLogger.warn('Persistence attempt failed', {
                  userId,
                })
                // Back-off delay using config constant
                // eslint-disable-next-line no-await-in-loop
                await new Promise(res =>
                  setTimeout(res, RETRY_CONFIG.BACKOFF_MS * attempt)
                )
              }

              if (!persistResult?.success) {
                // CRITICAL: Log this failure – user saw response but it wasn't saved
                chatLogger.error(
                  'FATAL: Failed to save conversation turn after all retries',
                  {
                    userId,
                  }
                )
              } else {
                chatLogger.info('Successfully persisted conversation turn', {
                  userId,
                })
              }

              // Log AI usage for monitoring (keep existing logic)
              try {
                await createAIUsage({
                  userId: userId,
                  entityType: 'aipane',
                  entityId: conversationId!,
                  aiProvider: 'azure_openai',
                  modelName: model,
                  usageType: AIUsageType.CHAT,
                  inputPrompt: latestUserMessage,
                  messages: [
                    ...modelMessages,
                    { role: 'assistant', content: finalResponse },
                  ],
                  metadata: {
                    endpoint: '/api/aipane/chat',
                    inputTokens: result.usage?.inputTokens || 0,
                    outputTokens: result.usage?.outputTokens || 0,
                    totalTokens: result.usage?.totalTokens || 0,
                    finishReason: result.finishReason,
                    hasContext: !!context,
                    messageCount: messages.length,
                    conversationId,
                    executionSteps: stepCollector.getStepCount(),
                    hasThinking:
                      (stepCollector.getStepsByType()['thinking'] || [])
                        .length > 0,
                    hasToolCalls:
                      (stepCollector.getStepsByType()['tool-call'] || [])
                        .length > 0,
                    searchResults: searchMetadata.length,
                  },
                  config: settings,
                })
              } catch (error) {
                // 1. preserve original Error, pass context separately
                chatLogger.error('Failed to log AI usage', error, {
                  userId,
                })
              }

              // Auto-generate title for new conversations (fire-and-forget)
              queueMicrotask(async () => {
                try {
                  const conversation = await prisma.aiConversation.findUnique({
                    where: { id: conversationId },
                    select: { title: true },
                  })

                  if (conversation?.title === 'Untitled chat') {
                    const { generateTitleWithWeakModel } = await import(
                      '@/app/libs/title-generation'
                    )
                    const title =
                      await generateTitleWithWeakModel(latestUserMessage)

                    await prisma.aiConversation.update({
                      where: { id: conversationId },
                      data: { title },
                    })

                    // Push title to client for live UI update
                    // Title push to client disabled temporarily
                  }
                } catch (error) {
                  // Don't fail the main request if title generation fails
                  chatLogger.error(
                    'Failed to generate conversation title',
                    error,
                    {
                      userId,
                    }
                  )
                }
              })
            } catch (error) {
              // CRITICAL: Log this failure - the user saw a response but it wasn't saved
              // 2. include the real Error object
              chatLogger.error(
                'FATAL: Failed to process conversation turn',
                error,
                { userId }
              )
            }
          },
        })

        // Stream the text content using AI SDK 5 proper method
        writer.merge(result.toUIMessageStream())
      },
    })

    // Use AI SDK helper to encode UIMessage chunks into SSE response
    return createUIMessageStreamResponse({ stream })
  } catch (error) {
    // 4. outer-most catch: surface the actual error for easier debugging
    chatLogger.error('Chat API error', error)
    return standardErrors.internalError('Error generating response')
  }
}
