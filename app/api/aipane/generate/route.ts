import {
  streamText,
  createUIMessageStream,
  createUIMessageStreamResponse,
} from 'ai'
import type { Msg } from '@/app/types/ai-sdk5'
import { azure } from '@ai-sdk/azure'
import { NextResponse } from 'next/server'
import { AIPaneGenerateRequest } from '@/app/api/aipane/generate/types'
import { createAIUsage } from '@/app/server-actions/log_ai_usage'
import { AIUsageType, AIGenerationStatus } from '@prisma/client'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/authOptions'
import prisma from '@/app/libs/prismadb'
import { generateIdWithPrefix } from '@/lib/id'
import { validateDragTreeOwnership } from '@/app/libs/validation-utils'
import { getModelConfig } from '@/app/libs/model-config'
import {
  getLLMLanguageName,
  type SupportedLanguageCode,
} from '@/app/(conv)/screening/constants/languages'

export const maxDuration = 60

/**
 * Converts template name to slugified format
 * "Explain to layman" → "explain_to_layman"
 */
function slugify(name?: string): string {
  if (!name) return 'custom_prompt'
  return (
    // trim underscores
    name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '_') // collapse to underscores
      .replace(/^_+|_+$/g, '') || 'custom_prompt'
  )
}

/**
 * Returns timestamp in yyyymmdd:hhss format
 */
function formatDate(d: Date): string {
  const pad = (n: number) => String(n).padStart(2, '0')
  return (
    `${d.getFullYear()}${pad(d.getMonth() + 1)}${pad(d.getDate())}:` +
    `${pad(d.getHours())}${pad(d.getSeconds())}`
  )
}

/**
 * Generate a smart title based on the prompt template and datetime
 * Format: "slugified_template_name - yyyymmdd:hhss"
 */
function generateSmartTitle(promptTemplate?: string): string {
  return `${slugify(promptTemplate)} - ${formatDate(new Date())}`
}

export async function POST(req: Request) {
  try {
    // Retrieve authenticated user session
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
    }
    const userId = session.user.id

    // Defensive parsing
    let requestBody: any = null
    try {
      requestBody = await req.json()
    } catch {
      requestBody = null
    }

    // Handle both chat message format (from useChat) and direct format
    let prompt: string
    let model: string = 'gpt-4.1'
    let settings: Record<string, any> = {}
    let dragTreeId: string
    let entityType: string = 'drag_tree_v1'
    let promptTemplate: string | undefined

    if (requestBody && requestBody.messages) {
      // Chat message format (from useChat)
      const userMessage = requestBody.messages.find(
        (msg: any) => msg.role === 'user'
      )
      if (!userMessage) {
        return NextResponse.json(
          { error: 'No user message found in chat format' },
          { status: 400 }
        )
      }
      if (requestBody.prompt) {
        prompt = requestBody.prompt
        model = requestBody.model || 'gpt-4.1'
        settings = requestBody.settings || {}
        dragTreeId = requestBody.dragTreeId
        entityType = requestBody.entityType || 'drag_tree_v1'
        promptTemplate = requestBody.promptTemplate
      } else {
        prompt = userMessage.content
        model = requestBody.model || 'gpt-4.1'
        settings = requestBody.settings || {}
        dragTreeId = requestBody.dragTreeId
        entityType = requestBody.entityType || 'drag_tree_v1'
        promptTemplate = requestBody.promptTemplate
      }
    } else if (requestBody) {
      // Direct format (original)
      const directRequest = requestBody as AIPaneGenerateRequest
      prompt = directRequest.prompt
      model = directRequest.model
      settings = directRequest.settings || {}
      dragTreeId = directRequest.dragTreeId
      entityType = directRequest.entityType || 'drag_tree_v1'
      promptTemplate = directRequest.promptTemplate
    } else {
      return NextResponse.json(
        { message: 'Invalid request body: prompt is required.' },
        { status: 400 }
      )
    }

    if (!prompt || typeof prompt !== 'string' || !prompt.trim()) {
      return NextResponse.json(
        { message: 'Invalid request body: prompt is required.' },
        { status: 400 }
      )
    }

    if (!dragTreeId || typeof dragTreeId !== 'string' || !dragTreeId.trim()) {
      return NextResponse.json(
        { message: 'Invalid request body: dragTreeId is required.' },
        { status: 400 }
      )
    }

    // Validate dragTree ownership and get language preference
    const validation = await validateDragTreeOwnership(userId, dragTreeId)
    if (!validation.success) {
      return validation.error
    }

    // Fetch drag tree to get language preference
    const dragTree = await prisma.dragTree.findUnique({
      where: { id: dragTreeId },
      select: { preferred_language: true },
    })

    const preferredLanguage =
      (dragTree?.preferred_language as SupportedLanguageCode) || 'en'
    const languageName = getLLMLanguageName(preferredLanguage)

    // Server-authoritative ID generation using the custom ID system
    // This is the single source of truth for AI generation IDs
    const generationId = generateIdWithPrefix('doc')

    console.log(
      '🤖 [AI Pane Real] Starting generation with model:',
      model,
      'Server ID:',
      generationId
    )

    // Get model configuration based on user's subscription tier
    const modelConfig = await getModelConfig(userId, 'aipane_generate')
    const finalModel = modelConfig.model

    // Create placeholder record in database before streaming
    // This ensures the ID exists and can be referenced immediately
    try {
      await prisma.aIGeneration.create({
        data: {
          id: generationId,
          user_id: userId,
          entity_type: entityType,
          entity_id: dragTreeId,
          status: AIGenerationStatus.GENERATING, // Actively streaming tokens
          title: 'Generating...', // Temporary title
          content: '', // Will be updated in onFinish
          generation_input: prompt,
          generation_output: '', // Will be updated in onFinish
          config: {
            model_name: finalModel,
            settings: settings,
          },
          metadata: {
            endpoint: '/api/aipane/generate',
            streamingStarted: true,
            streamingCompleted: false,
            is_read: false,
          },
        },
      })
      console.log(
        '✅ [AI Generation] Created placeholder record:',
        generationId
      )
    } catch (error) {
      console.error('❌ [AI Generation] Failed to create placeholder:', error)
      return NextResponse.json(
        { message: 'Failed to initialize generation' },
        { status: 500 }
      )
    }

    // Use AI SDK 5 createUIMessageStream for proper data parts streaming
    const stream = createUIMessageStream<Msg>({
      async execute({ writer }) {
        // Stream the LLM response
        const result = streamText({
          model: azure(finalModel),
          messages: [
            {
              role: 'system',
              content: `You are a helpful AI assistant. Generate comprehensive and well-structured content based on the user prompt. Use markdown formatting when appropriate, including tables, lists, and code blocks.

Please generate all content in ${languageName}.`,
            },
            {
              role: 'user',
              content: prompt,
            },
          ],
          temperature: modelConfig.temperature,
          maxOutputTokens: modelConfig.maxOutputTokens,
          onFinish: async result => {
            // Update existing placeholder record with final content
            try {
              const generatedContent = result.text || ''

              // Generate smart title based on template name and datetime
              const title = generateSmartTitle(promptTemplate)

              // Stream the title to frontend using AI SDK 5 data parts
              writer.write({
                type: 'data-title',
                data: {
                  title: title,
                  generationId: generationId,
                },
              })

              const MAX_GEN_RETRIES = 3
              let genAttempt = 0
              let updateSuccess = false

              while (genAttempt < MAX_GEN_RETRIES && !updateSuccess) {
                genAttempt++
                try {
                  // eslint-disable-next-line no-await-in-loop
                  await prisma.aIGeneration.update({
                    where: { id: generationId },
                    data: {
                      status: AIGenerationStatus.ACTIVE, // Update to active status
                      title: title,
                      content: generatedContent,
                      generation_output: generatedContent,
                      metadata: {
                        endpoint: '/api/aipane/generate',
                        streamingStarted: true,
                        streamingCompleted: true,
                        completedAt: new Date().toISOString(),
                        is_read: false,
                      },
                    },
                  })
                  updateSuccess = true
                } catch (retryErr) {
                  console.warn(
                    `⚠️  [AI Generation] Update attempt ${genAttempt} failed:`,
                    retryErr
                  )
                  // eslint-disable-next-line no-await-in-loop
                  await new Promise(res => setTimeout(res, 50 * genAttempt))
                }
              }

              if (!updateSuccess) {
                throw new Error('Failed to update generation after retries')
              }

              console.log(
                '✅ [AI Generation] Updated placeholder to final:',
                generationId
              )
            } catch (error) {
              console.error(
                '❌ [AI Generation] Failed to update placeholder:',
                error
              )

              // Mark as failed if update fails
              try {
                await prisma.aIGeneration.update({
                  where: { id: generationId },
                  data: {
                    status: AIGenerationStatus.INACTIVE,
                    metadata: {
                      endpoint: '/api/aipane/generate',
                      streamingStarted: true,
                      streamingCompleted: false,
                      error: 'Failed to update final content',
                      failedAt: new Date().toISOString(),
                    },
                  },
                })
              } catch (markFailError) {
                console.error(
                  '❌ [AI Generation] Failed to mark as failed:',
                  markFailError
                )
              }
            }

            // Log AI usage for monitoring
            try {
              await createAIUsage({
                userId: userId,
                entityType: entityType,
                entityId: dragTreeId,
                aiProvider: 'azure_openai',
                modelName: model,
                usageType: AIUsageType.GENERATE_QUESTION,
                inputPrompt: prompt,
                messages: [
                  {
                    role: 'system',
                    content: `System: ${languageName} assistant`,
                  },
                  { role: 'user', content: prompt },
                  { role: 'assistant', content: result.text || '' },
                ],
                metadata: {
                  endpoint: '/api/aipane/generate',
                  generationId: generationId,
                },
                config: settings,
              })
            } catch (error) {
              console.error('Failed to log AI usage:', error)
            }
          },
        })

        // Merge the LLM text stream into the UIMessage stream
        writer.merge(result.toUIMessageStream())
      },
    })

    console.log(
      '🚀 [AI Generation] Streaming response with ID header:',
      generationId
    )
    // Use AI SDK helper to format a proper SSE response
    const res = createUIMessageStreamResponse({ stream })
    // Propagate the generation ID for client-side tracking
    const headers = new Headers(res.headers)
    headers.set('X-Generation-Id', generationId)
    return new Response(res.body, { headers })
  } catch (error) {
    console.error('AI Pane generate API error:', error)
    return NextResponse.json({ message: 'Error' }, { status: 500 })
  }
}
