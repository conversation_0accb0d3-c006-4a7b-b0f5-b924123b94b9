export type SearchResult = {
  scripted_number: string
  title: string
  url: string
  description: string
  extra_snippets?: string[]
}

export type SearchResults = {
  web: {
    results: SearchResult[]
  }
}

export enum RAGDBOperations {
  LOG_SEARCH = 'logSearch',
  LOG_AND_SAVE_RESPONSE = 'logAndSaveResponse',
}

// Modify CustomBraveSearch to include a proper return type.
export async function CustomBraveSearch(
  searchQuery: string
): Promise<SearchResults> {
  const apiKey = process.env.BRAVE_SEARCH_API_KEY
  if (!apiKey) {
    throw new Error('API key is missing')
  }

  const headers = {
    'X-Subscription-Token': apiKey,
  }

  const searchUrl = new URL(
    `https://api.search.brave.com/res/v1/web/search?q=${encodeURIComponent(
      searchQuery
    )}`
  )

  const response = await fetch(searchUrl, { headers })

  if (!response.ok) {
    throw new Error(`HTTP error ${response.status}`)
  }

  return await response.json()
}

export const processSearchResults = (searchResults: SearchResults) => {
  const numMapping: { [key: string]: string } = {
    '0': '⁰',
    '1': '¹',
    '2': '²',
    '3': '³',
    '4': '⁴',
    '5': '⁵',
    '6': '⁶',
    '7': '⁷',
    '8': '⁸',
    '9': '⁹',
  }
  const resultsArray: SearchResult[] = []

  let cur = 1
  searchResults.web.results.forEach(webRes => {
    if (!webRes.extra_snippets) return

    const scriptedNumber = cur
      .toString()
      .split('')
      .map(digit => numMapping[digit])
      .join('')

    resultsArray.push({
      scripted_number: scriptedNumber,
      title: webRes.title,
      url: webRes.url,
      description: webRes.description,
      extra_snippets: webRes.extra_snippets,
    })
    cur++
  })
  return resultsArray
}

const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// Currently unused function - commented out to avoid ESLint warnings
// const retryRequest = async <T, A extends any[]>(
//   func: (...args: A) => Promise<T>,
//   args: A,
//   attempts: number = 3,
//   delayTime: number = 100
// ): Promise<T> => {
//   try {
//     return await func(...args);
//   } catch (error) {
//     if (attempts > 1) {
//       await delay(delayTime);
//       console.log(
//         `Retrying... Attempts left: ${
//           attempts - 1
//         }, delaying for ${delayTime} ms`
//       );
//       return retryRequest(func, args, attempts - 1, delayTime * 2);
//     } else {
//       throw new Error("Failed after multiple attempts: " + error);
//     }
//   }
// };
