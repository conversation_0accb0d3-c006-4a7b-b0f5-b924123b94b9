import { streamObject } from 'ai'
import { NextRequest, NextResponse } from 'next/server'
import { azure } from '@ai-sdk/azure'
import { AIUsageType } from '@prisma/client'
import {
  getLanguageName,
  type SupportedLanguageCode,
} from '@/app/constants/languages'
import { validateRequestBody } from '@/app/libs/validation-utils'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/authOptions'
import { enforceRateLimit } from '@/app/libs/llmRateLimit'
import { createAIUsage } from '@/app/server-actions/log_ai_usage'
import { screeningRequestSchema } from '@/app/libs/api-schemas'
import {
  getModelConfigForSession,
  DEFAULT_MAX_PROMPT_CHARS,
} from '@/app/libs/model-config'
import { z } from 'zod'

export const maxDuration = 60

// Define schema for the rephrase response - array of strings for the problem statements
const rephraseSchema = z.object({
  rephrases: z
    .array(z.string())
    .describe('Array of rephrased problem statements'),
})

export async function POST(req: NextRequest) {
  // ────────────────────── AUTH & RATE LIMIT ──────────────────────
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }
  // Enforce route-specific rate limit
  const rateLimitResp = await enforceRateLimit(session, 'screening_rephrase')
  if (rateLimitResp) return rateLimitResp

  try {
    // Validate request body using centralized validation
    const bodyResult = await validateRequestBody(req, screeningRequestSchema)
    if (!bodyResult.success) {
      return bodyResult.error
    }

    const { description, preferredLanguage } = bodyResult.data
    const userId = session.user.id
    const modelConfig = await getModelConfigForSession(
      session,
      'screening_rephrase'
    )
    const modelName = modelConfig.model

    // Prompt size guard
    const maxChars = modelConfig.maxPromptChars ?? DEFAULT_MAX_PROMPT_CHARS
    if (description.length > maxChars) {
      return NextResponse.json(
        { error: 'Description too long (max 1,000,000 characters)' },
        { status: 400 }
      )
    }

    const languageCode = (preferredLanguage as SupportedLanguageCode) || 'en'

    const languageName = getLanguageName(languageCode)
    const systemMessage = createSystemMessage(languageName, description)

    const result = streamObject({
      model: azure(modelName),
      messages: [
        {
          role: 'system',
          content: systemMessage,
        },
      ],
      schema: rephraseSchema,
      temperature: modelConfig.temperature,
      maxOutputTokens: modelConfig.maxOutputTokens,
      onFinish: async result => {
        console.log('✅ [Screening Rephrase] Rephrasing completed')

        // Log AI usage with full audit data
        try {
          await createAIUsage({
            userId: userId,
            entityType: 'screening',
            entityId: `screening_rephrase_${Date.now()}`, // Generate a unique ID for this rephrase session
            aiProvider: 'azure_openai',
            modelName: modelName,
            usageType: AIUsageType.SCREENING_REPHRASE,
            inputPrompt: description,
            messages: [
              { role: 'system', content: systemMessage },
              { role: 'assistant', content: JSON.stringify(result.object) },
            ],
            metadata: {
              language: languageCode,
              originalDescription: description,
              tokenUsage: result.usage,
            },
            config: {},
          })
        } catch (error) {
          console.error(
            '❌ [Screening Rephrase] Error logging AI usage:',
            error
          )
          // Don't fail the request if logging fails
        }

        console.log('🎉 [Screening Rephrase] AI usage logged successfully')
      },
    })

    return result.toTextStreamResponse()
  } catch (error) {
    console.error('Screening rephrase API error:', error)
    return NextResponse.json({ message: 'Error' }, { status: 500 })
  }
}

// Helper function to create the system message with language support
function createSystemMessage(
  languageName: string,
  description: string
): string {
  return `Given a user requirement, your task is to rephrase it into four different problem statements, each highlighting a unique aspect of the requirement. Return the rephrases as a JSON object with a "rephrases" array containing exactly 4 strings.

For each rephrased statement:
- **Begin with a focus label in brackets that dynamically reflects the main theme or unique aspect of that statement.** The focus label should be concise, specific, or utilize key terms from the user's requirement. Do not hardcode the labels; generate them based on the content.
- **Follow the specific instructions for each rephrased statement as detailed below.**

**Instructions:**
1. **Objective:** Restate the initial requirement clearly and concisely without missing any details.
   - **Focus Label:** Reflect the core objective or key feature.
2. **Objective:** Translate the initial requirement into a user story that highlights how fulfilling this requirement impacts a specific group within the organization.
   - **Focus Label:** Reflect the specific group and the impact.
3. **Objective:** Reframe the initial requirement as a user story that addresses potential risks or challenges implied by the strategic goals.
   - **Focus Label:** Reflect the specific risks or challenges.
4. **Objective:** Rewrite the initial requirement as a user story from a strategic/high-level perspective, emphasizing forward-looking goals and potential changes that may affect the organization.
   - **Focus Label:** Reflect the strategic/high-level perspective.

**User story format:** As <some role>, I want <some goal> so that <some achieved outcome>.

**Additional Guidelines:**
- **Include All Original Details:** Preserve all relevant details from the original requirement, including any figures, names, challenges, or keywords.
- **Self-Contained Statements:** Ensure each rephrased statement is self-contained and understandable on its own.
- **Formatting:** Start each rephrased statement with the bracketed focus label followed by the statement.
- **Tone and Tense:** Maintain the tone and tense used in the user's original statement.

User request: ${description}

Please generate the response in ${languageName}.
`
}
