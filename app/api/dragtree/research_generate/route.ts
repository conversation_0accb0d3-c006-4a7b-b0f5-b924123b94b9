/**
 * Research Generation API Endpoint
 *
 * Main API endpoint for AI-powered research generation with web search integration.
 * Uses Azure OpenAI GPT-4o-mini with Vercel AI SDK tool calling for web search.
 *
 * Key Features:
 * - Language detection from drag tree settings
 * - Brave Search API integration via buildSearchTools
 * - Streaming responses with onFinish callback
 * - Content metadata collection for frontend display
 * - Status management (INITIALIZED → PROCESSING → ACTIVE)
 * - Tool step limiting to ensure final answer generation
 *
 * Request Format Support:
 * - Chat message format (from useChat hook)
 * - Direct format (manual API calls)
 *
 * Critical Implementation Details:
 * - Uses request-scoped metadata collector for thread safety
 * - Reserved steps logic ensures AI provides final answer
 * - Comprehensive error handling with status updates
 */

import { streamText, stepCountIs } from 'ai'
import { NextResponse } from 'next/server'
import { azure } from '@ai-sdk/azure'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/authOptions'
import {
  Drag<PERSON>reeNodeContentStatus,
  <PERSON>risma,
  AIUsageType,
  DragTreeNodeContentType,
} from '@prisma/client'
import { createResearchPrompt } from '@/app/api/dragtree/shared/research-prompts'
import { createAIUsage } from '@/app/server-actions/log_ai_usage'
import {
  getLanguageName,
  type SupportedLanguageCode,
} from '@/app/(conv)/screening/constants/languages'
import { SearchMetadata } from '@/app/api/dragtree/shared/brave-search'
import prisma from '@/app/libs/prismadb'
import { buildSearchTools } from '@/app/api/dragtree/shared/search-tools'
import { getModelConfig } from '@/app/libs/model-config'
import { extractTextContent } from '@/app/types/ai-sdk5'

export const maxDuration = 60

export type ResearchGenerateRequestType = {
  contentId: string
  questionText: string
  researchType?: DragTreeNodeContentType
}

export async function POST(req: Request) {
  try {
    // Get session for authentication
    const session = await getServerSession(authOptions)
    const userId = session?.user?.id

    if (!userId) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
    }

    const requestBody = await req.json()

    // Handle both chat message format (from useChat) and direct format
    let contentId: string
    let questionText: string
    let researchType: string = DragTreeNodeContentType.QUICK_RESEARCH

    if (requestBody.messages) {
      // Chat message format (from useChat)
      const userMessage = requestBody.messages.find(
        (msg: any) => msg.role === 'user'
      )
      if (!userMessage) {
        return NextResponse.json(
          { error: 'No user message found in chat format' },
          { status: 400 }
        )
      }

      // Extract data from body parameters alongside messages
      if (requestBody.contentId && requestBody.questionText) {
        contentId = requestBody.contentId
        questionText = requestBody.questionText
        researchType =
          requestBody.researchType || DragTreeNodeContentType.QUICK_RESEARCH
      } else {
        // Try to parse from message content (JSON format)
        try {
          const parsedContent = JSON.parse(userMessage.content)
          contentId = parsedContent.contentId
          questionText =
            parsedContent.questionText ||
            parsedContent.question ||
            userMessage.content
          researchType =
            parsedContent.researchType || DragTreeNodeContentType.QUICK_RESEARCH
        } catch {
          return NextResponse.json(
            { error: 'Missing contentId in chat message format' },
            { status: 400 }
          )
        }
      }
    } else {
      // Direct format
      const directRequest = requestBody as ResearchGenerateRequestType
      contentId = directRequest.contentId
      questionText = directRequest.questionText
      researchType =
        directRequest.researchType || DragTreeNodeContentType.QUICK_RESEARCH
    }

    if (!contentId || !questionText) {
      return NextResponse.json(
        { error: 'Missing required fields: contentId, questionText' },
        { status: 400 }
      )
    }

    console.log(
      `🔬 [Research Generate] Starting research for content: ${contentId}`
    )

    // Fetch the drag tree to get preferred language and screening question
    const nodeContent = await prisma.dragTreeNodeContent.findUnique({
      where: { id: contentId },
      include: {
        drag_tree_node: {
          include: {
            drag_tree: true,
          },
        },
      },
    })

    if (!nodeContent?.drag_tree_node?.drag_tree) {
      throw new Error('Drag tree not found for content')
    }

    const dragTree = nodeContent.drag_tree_node.drag_tree

    console.log(
      `🔍 [Research Generate] Raw preferred_language from DB:`,
      dragTree.preferred_language,
      `(type: ${typeof dragTree.preferred_language})`
    )

    const preferredLanguage =
      (dragTree.preferred_language as SupportedLanguageCode) || 'en'
    const language = getLanguageName(preferredLanguage)
    const screeningQuestion = dragTree.user_prompt || undefined

    console.log(
      `🔍 [Research Generate] After processing: preferredLanguage="${preferredLanguage}", language="${language}"`
    )

    console.log(
      `🌍 [Research Generate] Language detected: ${preferredLanguage} -> "${language}", Screening: ${screeningQuestion ? 'Yes' : 'No'}`
    )
    console.log(
      `📝 [Research Generate] Prompt will end with: "Please generate the language in ${language}"`
    )

    // Get model configuration based on user's subscription tier
    const modelConfig = await getModelConfig(
      userId,
      'dragtree_research_generate'
    )
    const model_name = modelConfig.model

    // --- REFACTORED METADATA HANDLING ---
    // 1. Create a single, authoritative metadata object upfront.
    const generationMetadata = {
      researchType,
      originalQuestion: questionText,
      model: model_name,
      language: preferredLanguage,
      startedAt: new Date().toISOString(),
      completedAt: '', // Will be set on finish
      hasWebSearch: false, // Will be updated on finish
      tokenUsage: {
        inputTokens: 0,
        outputTokens: 0,
        totalTokens: 0,
      },
    }

    // Update content status to PROCESSING
    await prisma.dragTreeNodeContent.update({
      where: { id: contentId },
      data: {
        status: DragTreeNodeContentStatus.PROCESSING,
        // Store the initial metadata
        generation_metadata: generationMetadata,
      },
    })

    const systemMessage = createResearchPrompt(
      questionText,
      screeningQuestion,
      language
    )

    console.log(
      `🔍 [Research Generate] Generated prompt preview (last 200 chars):`,
      systemMessage.slice(-200)
    )

    // Request-scoped metadata collector for thread safety
    // Each API request gets its own collector to prevent cross-request contamination
    const collectedSearchMetadata: SearchMetadata[] = []

    // AI SDK v5: Use stopWhen to control multi-step execution
    // Allow up to 10 steps, but ensure the model provides a final answer
    const MAX_STEPS = 10

    // Use Vercel AI SDK streamText with web search tools
    const result = streamText({
      model: azure(model_name),
      messages: [
        {
          role: 'system',
          content: systemMessage,
        },
      ],
      tools: buildSearchTools(collectedSearchMetadata),
      // AI SDK v5: Use stopWhen instead of maxSteps for server-side control
      stopWhen: stepCountIs(MAX_STEPS),
      temperature: modelConfig.temperature,
      maxOutputTokens: modelConfig.maxOutputTokens,
      // AI SDK v5: Use stable prepareStep API (no longer experimental)
      prepareStep: async ({ stepNumber }: { stepNumber: number }) => {
        // Reserve the last 2 steps for final answer generation
        const RESERVED_STEPS = 2
        const remaining = MAX_STEPS - stepNumber
        if (remaining <= RESERVED_STEPS) {
          // Disable tools for the last reserved step(s) to force final answer
          return {
            toolChoice: 'none',
            // AI SDK v5: Use activeTools (no longer experimental)
            activeTools: [],
          }
        }
        return undefined
      },
      onFinish: async result => {
        try {
          console.log(
            `✅ [Research Generate] Completed research for content: ${contentId}`
          )

          const searchMetadata = collectedSearchMetadata
          console.log(
            `📊 [Research Generate] Collected ${searchMetadata.length} search metadata entries`
          )

          // Update metadata with completion details
          generationMetadata.completedAt = new Date().toISOString()
          generationMetadata.tokenUsage = {
            inputTokens: (result.usage as any)?.promptTokens || 0,
            outputTokens: (result.usage as any)?.completionTokens || 0,
            totalTokens:
              ((result.usage as any)?.promptTokens || 0) +
              ((result.usage as any)?.completionTokens || 0),
          }
          generationMetadata.hasWebSearch = searchMetadata.length > 0

          const contentMetadata = {
            searchResults: searchMetadata,
            generationInfo: generationMetadata,
          }

          // Store complete conversation history including system prompt for future continuation
          const completeConversation = [
            {
              role: 'system',
              content: systemMessage,
            },
            ...result.response.messages,
          ]

          // Extract final text with fallback logic
          let finalText: string = (
            typeof result.text === 'string' ? result.text : ''
          ).trim()

          // Fallback: Extract from assistant messages
          if (!finalText) {
            const assistantMessages =
              result.response.messages?.filter(m => m.role === 'assistant') ||
              []

            finalText = assistantMessages
              .map(m => {
                let extracted = ''

                // Handle different message formats from AI SDK
                if (typeof m.content === 'string') {
                  extracted = m.content
                } else if (Array.isArray(m.content)) {
                  // Extract text and tool result content
                  extracted = m.content
                    .filter(part => {
                      return (
                        ((part as any).type === 'text' && (part as any).text) ||
                        ((part as any).type === 'tool-result' &&
                          (part as any).result)
                      )
                    })
                    .map(part => {
                      if ((part as any).text) {
                        return (part as any).text
                      }
                      if ((part as any).result) {
                        return typeof (part as any).result === 'string'
                          ? (part as any).result
                          : JSON.stringify((part as any).result)
                      }
                      return ''
                    })
                    .join('')
                }

                return extracted
              })
              .filter(Boolean)
              .join('\n')
              .trim()
          }

          // Last resort: Use search results if no text content
          if (!finalText && searchMetadata.length > 0) {
            const searchSummary = searchMetadata
              .slice(0, 3)
              .map(result => `**${result.url}**\n${result.snippets.join(' ')}`)
              .join('\n\n')

            if (searchSummary.trim()) {
              finalText = `Based on the search results, here are the key findings:\n\n${searchSummary}\n\n*Note: This response was generated from search results due to an AI processing issue.*`
            }
          }

          // Final fallback to avoid empty DB writes
          if (!finalText) {
            finalText = '[No answer returned]'
          }

          // Update content and log AI usage in parallel
          await Promise.all([
            prisma.dragTreeNodeContent.update({
              where: { id: contentId },
              data: {
                status: DragTreeNodeContentStatus.ACTIVE,
                content_text: finalText, // ← use robust text
                content_metadata: contentMetadata,
                messages:
                  completeConversation as unknown as Prisma.InputJsonValue,
                generation_metadata: generationMetadata, // Overwrite with the completed metadata
              },
            }),
            createAIUsage({
              userId: userId,
              entityType: 'drag_tree_node_content',
              entityId: contentId,
              aiProvider: 'azure_openai',
              modelName: model_name,
              usageType: AIUsageType.NODE_QUICK_RESEARCH,
              inputPrompt: systemMessage,
              messages: completeConversation,
              metadata: {
                tokenUsage: result.usage,
                searchMetadataCount: searchMetadata.length,
                hasWebSearch: searchMetadata.length > 0,
                language: preferredLanguage,
                researchType,
              },
              config: {
                maxSteps: MAX_STEPS,
                reservedSteps: 2, // RESERVED_STEPS constant
              },
            }),
          ])

          console.log(
            `🎉 [Research Generate] Successfully saved research to DB for content: ${contentId} with ${searchMetadata.length} search metadata entries`
          )
        } catch (error) {
          console.error(`💥 [Research Generate] Error saving to DB:`, error)

          // Mark as failed if DB update fails
          await prisma.dragTreeNodeContent.update({
            where: { id: contentId },
            data: {
              status: DragTreeNodeContentStatus.INACTIVE,
              generation_metadata: {
                error: error instanceof Error ? error.message : 'Unknown error',
                failedAt: new Date().toISOString(),
              },
            },
          })
        }
      },
    })

    return result.toUIMessageStreamResponse({
      onFinish: async ({ messages }) => {
        try {
          console.log(
            `✅ [Research Generate] UI Stream completed for content: ${contentId}`
          )

          // Extract the assistant message from the UI messages
          const assistantMessage = messages.find(
            msg => msg.role === 'assistant'
          )
          if (assistantMessage) {
            // Extract final text from the assistant message
            const finalText = extractTextContent(assistantMessage)

            // Update the database with the final content and messages
            await prisma.dragTreeNodeContent.update({
              where: { id: contentId },
              data: {
                status: DragTreeNodeContentStatus.ACTIVE,
                content_text: finalText,
                messages: messages as unknown as Prisma.InputJsonValue,
                generation_metadata: generationMetadata,
              },
            })

            console.log(
              `🎉 [Research Generate] Successfully saved research to DB for content: ${contentId}`
            )
          } else {
            console.error(
              '❌ [Research Generate] No assistant message found in UI stream'
            )
          }
        } catch (error) {
          console.error(
            `💥 [Research Generate] Error in UI stream onFinish:`,
            error
          )
        }
      },
    })
  } catch (error) {
    console.error('💥 [Research Generate] API error:', error)

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
